# 报账明细电量推送接口说明

## 概述

新增了专门用于推送全部报账明细电量的接口，支持根据期号查询对应的 `collectmeter_v2_期号` 表，并推送全部数据到集团系统。

## 接口列表

### 1. 推送全部报账明细电量

**接口地址：** `POST /mssaccount/collectMeterSync/syncAllByPeriod`

**参数：**
- `periodNumber`: 期号（必填），格式为6位数字，例如：202507

**示例请求：**
```
POST /mssaccount/collectMeterSync/syncAllByPeriod?periodNumber=202507
```

**功能说明：**
- 查询 `collectmeter_v2_202507` 表的全部数据
- 转换为 CollectMeter 对象格式
- 批量推送到集团系统（每批50条）
- 返回推送结果统计

### 2. 查询期号表统计信息

**接口地址：** `GET /mssaccount/collectMeterSync/getTableStats`

**参数：**
- `periodNumber`: 期号（必填），格式为6位数字，例如：202507

**示例请求：**
```
GET /mssaccount/collectMeterSync/getTableStats?periodNumber=202507
```

**功能说明：**
- 查询指定期号表的数据统计信息
- 返回总数据量、已同步数量、未同步数量等信息

### 3. 分页推送报账明细电量

**接口地址：** `POST /mssaccount/collectMeterSync/syncByPage`

**参数：**
- `periodNumber`: 期号（必填），格式为6位数字，例如：202507
- `pageSize`: 每页大小（可选），默认1000，范围1-5000
- `maxPages`: 最大页数（可选），不设置则推送全部

**示例请求：**
```
POST /mssaccount/collectMeterSync/syncByPage?periodNumber=202507&pageSize=1000&maxPages=10
```

**功能说明：**
- 支持大数据量分页推送
- 每页之间有1秒间隔，避免推送过快
- 适用于数据量特别大的情况

## 数据表结构

接口查询的是 `collectmeter_v2_期号` 表，表结构包含以下主要字段：

- `collect_time`: 采集时间
- `start_date`: 开始日期
- `end_date`: 结束日期
- `total_quantity_of_electricity`: 总电量
- `city_code`, `city_name`: 市局编码和名称
- `county_code`, `county_name`: 区县编码和名称
- `station_code`, `station_name`: 局站编码和名称
- `ac_data`: 市电电量
- `energy_data`: 总能耗
- `device_data`: 设备能耗
- `production_data`: 生产分摊能耗
- 其他各类能耗数据字段

## 推送数据格式

推送到集团系统的数据格式为 CollectMeter 对象，包含：

```json
{
  "collectTime": "202507",
  "cityCode": "2600",
  "cityName": "辽宁-省公司",
  "countyCode": "**********",
  "countyName": "沈阳市浑南区分公司本部",
  "stationCode": "LH2600201912101822291575973349320013000",
  "stationName": "呼叫中心员工休息用房租赁",
  "energyData": "21760.00",
  "acData": "21760.00",
  "deviceData": "20672.00",
  "productionData": "21760.00",
  // ... 其他字段
}
```

## 使用示例

### 1. 推送202507期号的全部数据

```bash
curl -X POST "http://localhost:8080/mssaccount/collectMeterSync/syncAllByPeriod?periodNumber=202507"
```

### 2. 查看202507期号表的统计信息

```bash
curl -X GET "http://localhost:8080/mssaccount/collectMeterSync/getTableStats?periodNumber=202507"
```

### 3. 分页推送（每页500条，最多推送5页）

```bash
curl -X POST "http://localhost:8080/mssaccount/collectMeterSync/syncByPage?periodNumber=202507&pageSize=500&maxPages=5"
```

## 注意事项

1. **期号格式**：必须是6位数字，如202507（表示2025年7月）
2. **表名动态**：接口会根据期号动态查询对应的表，如 `collectmeter_v2_202507`
3. **批量推送**：数据会分批推送，每批50条，避免单次推送数据量过大
4. **错误处理**：如果表不存在或无数据，会返回相应的错误信息
5. **日志记录**：所有操作都有详细的日志记录，便于问题排查

## 与原有接口的区别

- **原有接口** (`/syncCollectMeterByCountyAndWriteoff`)：根据期号和报账单编码查询特定数据
- **新接口** (`/syncAllByPeriod`)：根据期号查询整个表的全部数据，无需指定报账单编码
- **数据来源**：新接口直接从 `collectmeter_v2_期号` 表读取，数据格式与推送格式一致
- **推送对象**：使用相同的 CollectMeter 对象和推送方法，保证数据格式兼容
