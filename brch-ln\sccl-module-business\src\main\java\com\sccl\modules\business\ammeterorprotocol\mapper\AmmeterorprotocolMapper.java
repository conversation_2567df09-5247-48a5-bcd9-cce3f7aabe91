package com.sccl.modules.business.ammeterorprotocol.mapper;

import com.sccl.framework.mapper.BaseMapper;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.accountEs.domain.PowerAccountEs;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.ammeterorprotocol.domain.StatisticalElectricityDTO;
import com.sccl.modules.business.ammeterorprotocol.domain.StatisticalElectricityRequest;
import com.sccl.modules.business.ammeterorprotocol.dto.AmmeterorprotocolDto;
import com.sccl.modules.business.stationinfo.domain.PowerStationInfoRJtlte;
import com.sccl.modules.business.timing.api.ElectricityEntry;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssinterface.domain.*;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 基础数据-电管理（注意该中所有的引用类型，
 * 都是指的power_category_type里面的type_code而非
 * ID，关注每个字段的注释） 数据层
 *
 * <AUTHOR>
 * @date 2019-04-28
 */
public interface AmmeterorprotocolMapper extends BaseMapper<Ammeterorprotocol> {
    /**
     * PUE获取电表协议
     */
    public List<Ammeterorprotocol> selectAmmeterListByPUE(Map<String, Object> params);

    /**
     * 获取有效的电表/协议 添加定额
     * 电表：未新增过定额的所有电表
     * 协议：未新增过定额的所有支出类有表协议
     */
    public List<Ammeterorprotocol> getAmmeterProtocolList(Ammeterorprotocol ammeterorprotocol);

    /**
     * 获取有效的电表/协议 添加电表/协议
     *
     * @param ammeterorprotocol
     * @return
     */
    public List<Ammeterorprotocol> getParentAmmeterProtocolList(Ammeterorprotocol ammeterorprotocol);

    /**
     * 获取局站信息
     */
    public List<Map<String, Object>> getListStation(Ammeterorprotocol ammeterorprotocol);

    public List<Ammeterorprotocol> selectListBy(Ammeterorprotocol ammeterorprotocol);

    public List<Map<String, Long>> getAmmeterorProtocolCategory(@Param("companies") List<IdNameVO> companies);

    public List<Map<String, Long>> getAountmoneyCategory(String company);

    public List<Map<String, Long>> getstationAountmoneydataprov(String company);

    public List<Map<String, Long>> getstationAountmoneydatacity(String company);

    public List<Map<String, Long>> getstationAountmoneydatacountry(String company);

    public List<Map<String, Long>> getstationAountmoneyzydata(String company);

    public List<Map<String, Long>> getstationAountmoneysfdata(String company);

    public List<Map<String, Long>> getstationAountmoneytadata(String company);

    public List<Ammeterorprotocol> selectByList(Ammeterorprotocol ammeterorprotocol);

    public int countatt(Long id);

    public List<Ammeterorprotocol> selectByListLN(Ammeterorprotocol ammeterorprotocol);

    List<Ammeterorprotocol> checkammetername(String[] ids);

    /**
     * 获取台账未录入预警列表
     *
     * @param ammeterorprotocol
     * @return
     */
    public List<Ammeterorprotocol> selectNoAccountList(Ammeterorprotocol ammeterorprotocol);

    /**
     * 电表协议个数统计
     *
     * @return
     */
    public List<Map<String, Object>> statistics(Map<String, Object> params);

    /**
     * 电表协议个数统计 详情
     *
     * @return
     */
    public List<Map<String, Object>> statisticsDetail(Map<String, Object> params);

    /**
     * 电表协议异常个数统计
     *
     * @return
     */
    public List<Map<String, Object>> statisticsException(Map<String, Object> params);

    /**
     * 电表协议异常个数统计 详情
     *
     * @return
     */
    public List<Map<String, Object>> statisticsExceptionDetail(Map<String, Object> params);

    /**
     * 获取最新一条电表编号
     *
     * @return
     */
    public String getAmmeterNo(Map<String, Object> params);

    /**
     * 根据局站获取关联的电表信息
     *
     * @param ammeterorprotocol
     * @return
     */
    public List<Ammeterorprotocol> getAmmeterListByStation(Ammeterorprotocol ammeterorprotocol);


    /**
     * 接口 上传电表信息到集团
     *
     * @param ammeterorprotocol
     * @return
     */
    public List<MeterInfo> getMeterInfoByAmmeter(Ammeterorprotocol ammeterorprotocol);

    /**
     * 同步计量设备
     *
     * @param ammeterorprotocol
     * @return
     */
    public List<MeterInfo2> getMeterInfoByAmmeter2ForSc(Ammeterorprotocol ammeterorprotocol);

    public List<MeterInfo2> getMeterInfoByAmmeter2ForLn(Ammeterorprotocol ammeterorprotocol);

    int createMeterInfoByAmmeter2ForSc(Ammeterorprotocol ammeterorprotocol);

    int createMeterInfoByAmmeter2ForLn(Ammeterorprotocol ammeterorprotocol);

    /**
     * 接口 上传电表信息到集团company
     *
     * @param ammeterorprotocol
     * @return
     */
    public List<MeterInfo> getMeterInfosend(Ammeterorprotocol ammeterorprotocol);

    /**
     * 获取被换电表是否已经换表（只要有数据就行）
     *
     * @param ammeterorprotocol
     * @return
     */
    public List<Ammeterorprotocol> selectChangeAmmeter(Ammeterorprotocol ammeterorprotocol);

    List<Ammeterorprotocol> selectListCheck(Ammeterorprotocol ammeterorprotocol);

    void setOpreatorsToCheck(HashMap<String, Object> map);

    void callproammetercheckdown(Ammeterorprotocol ammeterorprotocol);

    //基础数据稽核跑数据 过程
    void callproammeterCheck();

    List<HashMap<String, Object>> getHaveMoreThanOneAmmeter(Long billId);

    List<HashMap<String, Object>> getJT4gmap(Long billId);


    MssAccountbill selectMssBill(Long id);

    /**
     * 查询项目名称、详细地址作为单据属性
     *
     * @param code 直供电的户号，转供电的电表编号
     * @return com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol
     * <AUTHOR> Yongxiang
     * @date 2021/11/15 16:29
     */
    Ammeterorprotocol findBillParams(@Param("code") String code);

    /**
     * 同步计量设备信息模板 获取数据
     *
     * @param year
     * @return
     */
    List<MeterEquipmentInfo2> getMeterEquipmentInfos(@Param("year") Integer year);

    List<MeterEquipmentInfo2> getMeterEquipmentInfos2(@Param("pid") Long pid);

    List<MeterEquipmentInfo2> getMeterEquipmentInfos2ForLn(@Param("pid") Long ammeterorprotocolId);

    /**
     * 同步省能耗管理平台中抄表数据
     *
     * @param year
     * @return
     */
    List<CopyMeter> getCopyMeterInfors(@Param("billId") Long billId);

    List<CopyMeter> getCopyMeterInforsForLn(@Param("billId") Long billId);

    /**
     * 同步智能采集的电量数据
     *
     * @param year
     * @return
     */
    List<CollectMeter> getCollectMeterInfors(@Param("pcid") Long pcid);

    List<CollectMeter> getCollectMeterInfors2(@Param("pcid") Long pcid, @Param("time") String collectTime);

    List<CollectMeter> getCollectMeterInfors2Pro(@Param("pcid") Long pcid, @Param("time") String collectTime, @Param("budget") String budget);

    List<CollectMeter> getCollectMeterInforLnPlus(@Param("pcid") Long pcid, @Param("time") String collectTime, @Param("budget") String budget, @Param("list") List<String> stationcodes);
    List<CollectMeter> getCollectMeterInforLnPlusPro(@Param("pcid") Long pcid, @Param("time") String collectTime, @Param("budget") String budget, @Param("list") List<String> stationcodes);
    List<CollectMeter> getCollectMeterInforScPlusPro(@Param("pcid") Long pcid, @Param("time") String collectTime, @Param("budget") String budget, @Param("list") List<String> stationcodes);



    List<CollectMeter> getCollectMeterInforScPlusProPro(@Param("pcid") Long pcid, @Param("time") String collectTime, @Param("budget") String budget, @Param("list") List<String> stationcodes);
    List<CollectMeter> getCollectMeterInforScPlusProProGroupAmmeterpol(@Param("pcid") Long pcid, @Param("time") String collectTime, @Param("budget") String budget, @Param("list") List<String> stationcodes);
    List<CollectMeter> getCollectMeterInforScPlusProProGroupStationCode(@Param("pcid") Long pcid, @Param("time") String collectTime, @Param("budget") String budget, @Param("list") List<String> stationcodes);
    List<CollectMeter> getCollectMeterInforScPlusProProGroupAll(@Param("pcid") Long pcid, @Param("time") String collectTime, @Param("budget") String budget, @Param("list") List<String> stationcodes);
    List<CollectMeter> getCollectMeterInforScPlusProProGroupjwcheck(@Param("pcid") Long pcid, @Param("time") String collectTime, @Param("budget") String budget, @Param("list") List<String> stationcodes);




    List<CollectMeter> getCollectMeterInforScPlusProProPro(@Param("pcid") Long pcid, @Param("time") String collectTime, @Param("budget") String budget, @Param("list") List<String> stationcodes);
    List<CollectMeter> getCollectMeterInforScPlusProProProConsist(@Param("pcid") Long pcid, @Param("time") String collectTime, @Param("budget") String budget, @Param("list") List<String> stationcodes);
    List<CollectMeter> getCollectMeterInfors2SC(@Param("pcid") Long pcid, @Param("time") String collectTime);

    List<CollectMeter> getCollectMeterInforScPlus(@Param("pcid") Long pcid, @Param("time") String collectTime, @Param("list") List<String> stationcodes);

    List<MeterEquipmentInfo2> getMeterEquipmentInfosForSc(@Param("month") Integer month);

    List<MeterEquipmentInfo2> getMeterEquipmentInfosForLn(@Param("month") Integer month);

    List<CopyMeter> getCopyMeterInforsForScCron(@Param("month") int month);

    List<CopyMeter> getCopyMeterInforsForLnCron(@Param("month") int month);

    void CallpowerStationAvedayelec(@Param("maxMonth") Integer maxMonth, @Param("minMonth") Integer min);

    /**
     * 获取计量设备所有数据
     *
     * @param id
     * @param j
     * @param i
     * @return
     */
    List<MeterInfo3> selectMeterInfo(@Param("id") Long id, @Param("offset") int offset, @Param("size") int size);

    void deleteMeterIfo();

    Ammeterorprotocol getById(@Param("id") Long ammeterorprotocolId);

    /**
     * 单价项目 电表异常工单
     *
     * @param ammeterorprotocol
     * @return
     */
    List<Ammeterorprotocol> exceptionExport(@Param("ammeterorprotocol") Ammeterorprotocol ammeterorprotocol);

    List<? extends MeterInfo2> selectMeterInfoForCompany();

    List<? extends MeterInfo2> selectMeterInfoForNH();

    List<? extends MeterInfo2> selectMeterInfoAll(@Param("id") Long id, @Param("offset") int offset,
                                                  @Param("size") int size);

    List<CollectMeter> getCollectMeterInforsSCpage(@Param("pcid") Long pcid, @Param("offset") int offset, @Param(
      "size") int size);

    List<CollectMeter> getCollectMeterInforsSC(@Param("pcid") Long pcid);

    Integer getCollectMeterInforscountSC(@Param("pcid") Long pcid);

    /**
     * 根据county_name和WRITEOFF_INSTANCE_CODE查询结果列表
     * @param periodNumber 期号
     * @param writeoffInstanceCode 核销实例编码
     * @return 查询结果列表
     */
    List<CollectMeterQueryResult> getCollectMeterByCountyAndWriteoff(@Param("periodNumber") String periodNumber, @Param("writeoffInstanceCode") String writeoffInstanceCode);

    /**
     * 根据期号查询collectmeter_v2_期号表的全部数据
     * @param periodNumber 期号，例如：202507
     * @return 查询结果列表
     */
    List<CollectMeterQueryResult> getAllCollectMeterByPeriod(@Param("periodNumber") String periodNumber);

    List<? extends MeterInfo2> selectMeterInfoFail(@Param("id") Long id, @Param("offset") int offset,
                                                   @Param("size") int size);

    int insertByCollectMeter();

    void deleteMeterIfoTwoc();

    int createMeterInfoTowCByAmmeter2ForLn(Ammeterorprotocol m);

    int createMeterInfoTowCByAmmeter2ForSc(Ammeterorprotocol m);

    List<ElectricityEntry> selectElectricityType(List<String> id);

    List<PowerStationInfoRJtlte> getOneAmMoreSta(@Param("ammetercode") String ammetercode);

    List<AmmeterorprotocolDto> getOneStaMoreAm(@Param("ammetercode") String ammetercode);

    List<Ammeterorprotocol> getEleTypeByAmmeterCodes(@Param("ammeterCodes") List<String> ammeterCodes);

    Ammeterorprotocol getProjectNameExist(@Param("ammeterorprotocol") Ammeterorprotocol ammeterorprotocol);

    List<StatisticalElectricityDTO> listStatisticalElectricity(StatisticalElectricityRequest request);

    List<Ammeterorprotocol> selectByAccountIds(@Param("accountIds") List<Long> accountIds);

    void clearCollectMeterByCode(@Param("collectMeter") CollectMeter collectMeter);

    void batchInsertAllCollectMeterInfo(@Param("list") List<CollectMeter> collectMeterInfors);

    Ammeterorprotocol getAmmeterByCode(@Param("ammeterCode") String ammeterCode);

    List<Long> getPowerAccountByAmmeter(@Param("ammeterIds") List<Long> ammeterIds);
    List<PowerAccountEs> getPowerAccountEsByAmmeter(@Param("ammeterIds") List<Long> ammeterIds);

    List<MssAccountbill> selectMssBillByAmmeterIds(@Param("ammeterIds") long[] ammeterIds);

    /**
     * 查询智能电表数据
     * @param collectTime 采集时间
     * @return 智能电表数据列表
     */
    List<CollectMeter> getSmartMeterData(@Param("collectTime") String collectTime);

    /**
     * 根据countyCode和stationCode查询collectmeter表中的记录
     * @param countyCode 区县编码
     * @param stationCode 局站编码
     * @return 采集表记录
     */
    CollectMeter getCollectMeterByCountyAndStation(@Param("countyCode") String countyCode,
                                                   @Param("stationCode") String stationCode);

    /**
     * 更新collectmeter表记录
     * @param collectMeter 采集表记录
     * @return 更新条数
     */
    int updateCollectMeter(CollectMeter collectMeter);

    /**
     * 插入collectmeter表记录
     * @param collectMeter 采集表记录
     * @return 插入条数
     */
    int insertCollectMeter(CollectMeter collectMeter);
}

