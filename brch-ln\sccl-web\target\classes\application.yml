# 项目相关配置
sccl:
  #名称
  name: sccl-basic-frame
  #版本
  version: 1.0
  #版权年份
  copyrightYear: 2018
  # 获取ip地址开关
  addressEnabled: true
  #jwt 过期时间 单位 分钟
  jwtExpiredTime: 720
  #操作过期时间 单位分钟 超过这个时间没有操作 不再token续期
  opExpiredTime: 720
  #jwt 加密密钥
  jwtkey: sccl-web-jwt-encrypt-key

#开发环境配置
server:
  #服务端口
  port: 8080
  servlet:
    # 项目contextPath
    context-path: /energy-cost

#用户配置
user:
  password:
    #密码错误{maxRetryCount}次锁定10分钟
    maxRetryCount: 5
#Spring配置
spring:
  main:
    allow-bean-definition-overriding: true
  thymeleaf:
    mode: HTML
    encoding: utf-8
    # 禁用缓存
    cache: false
  messages:
    #国际化资源文件路径
    basename: i18n/messages
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  profiles:
    active: loc-ln
  #文件上传
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 30MB
  devtools:
    restart:
      #热部署开关
      enabled: false

mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.sccl.modules
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mybatis/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper 单数据源
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql


# Shiro
shiro:
  user:
    # 登录地址
    loginUrl: /login/login
    # 权限认证失败地址
    unauthorizedUrl: /unauth
    # 首页地址
    indexUrl: /index
    #后台授权
    authz: false,
    # 验证码开关
    captchaEnabled: false
    # 验证码类型 math 数组计算 char 字符
    captchaType: math
  cookie:
    # 设置Cookie的域名 默认空，即当前访问的域名
    domain:
    # 设置cookie的有效访问路径
    path: /
    # 设置HttpOnly属性
    httpOnly: true
    # 设置Cookie的过期时间，天为单位
    maxAge: 30
  session:
    # Session超时时间（默认30分钟）
    expireTime: 720
    # 同步session到数据库的周期（默认1分钟）
    dbSyncPeriod: 1
    # 相隔多久检查一次session的有效性，默认就是10分钟
    validationInterval: 10
# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/*,/uniflow/*,/auth/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

#雪花ID生成器版本
snowflakeID: 1.6

logging:
  level:
    cn.zhxu.bs: DEBUG
    io.github.mymonstercat.*: DEBUG

bean-searcher:
  params:
    pagination:
      page: current
      size: size
      start: 1
