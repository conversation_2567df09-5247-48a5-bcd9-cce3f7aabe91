package com.sccl.modules.uniflow.wftask.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sccl.common.io.PropertiesUtils;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.common.exception.workflow.UniflowException;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.service.IdGenerator;
import com.sccl.framework.utils.RestTemplateUtil;
import com.sccl.framework.utils.WebServiceClient;
import com.sccl.framework.utils.enumClass.CommonConstants;
import com.sccl.modules.system.user.domain.User;
import com.sccl.modules.system.user.mapper.UserMapper;
import com.sccl.modules.system.user.service.UserServiceImpl;
import com.sccl.modules.uniflow.common.WFModel;
import com.sccl.modules.uniflow.wfdorecord.domain.WfDoRecord;
import com.sccl.modules.uniflow.wfdorecord.service.IWfDoRecordService;
import com.sccl.modules.uniflow.wfoperlog.domain.WfOperLog;
import com.sccl.modules.uniflow.wfprocconfig.domain.WfProcConfig;
import com.sccl.modules.uniflow.wfprocconfig.service.IWfProcConfigService;
import com.sccl.modules.uniflow.wfprocinst.domain.WfProcInst;
import com.sccl.modules.uniflow.wfprocinst.service.IWfProcInstService;
import com.sccl.modules.uniflow.wfprocrecord.domain.WfProcRecord;
import com.sccl.modules.uniflow.wfprocrecord.service.IWfProcRecordService;
import com.sccl.modules.uniflow.wfsendoalog.domain.WfSendoaLog;
import com.sccl.modules.uniflow.wfsendoalog.mapper.WfSendoaLogMapper;
import com.sccl.modules.uniflow.wftask.domain.SyncUnitaskVO;
import com.sccl.modules.uniflow.wftask.domain.WfTask;
import com.sccl.modules.uniflow.wftask.domain.WfTaskDTO;
import com.sccl.modules.uniflow.wftask.domain.WfTaskQuery;
import com.sccl.modules.uniflow.wftask.mapper.WfTaskMapper;
import com.sccl.modules.uniflow.wftaskassignee.domain.WfTaskAssignee;
import com.sccl.modules.uniflow.wftaskassignee.service.IWfTaskAssigneeService;
import com.sccl.modules.uniflow.wftaskassigneehis.domain.WfTaskAssigneeHis;
import com.sccl.modules.uniflow.wftaskassigneehis.service.IWfTaskAssigneeHisService;
import com.sccl.modules.uniflow.wftaskhis.domain.WfTaskHis;
import com.sccl.modules.uniflow.wftaskhis.service.IWfTaskHisService;
import com.sccl.modules.util.WFConstants;
import com.sccl.modules.util.WorkflowUtil;
import org.jsoup.helper.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.InputStream;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * 任务实例 服务层实现
 *
 * <AUTHOR>
 * @date 2019-03-15
 */
@Service
public class WfTaskServiceImpl extends BaseServiceImpl<WfTask> implements IWfTaskService {
    private static final Logger logger = LoggerFactory.getLogger(WfTaskServiceImpl.class);
    @Value("${is.sync.complete.task}")
    Boolean isSyncCompleteTask;

    @Value("${proc.inst.url}")
    String procInstUrl;

    @Value("${pi.url}")
    String piUrl;

    @Value("${DO_CASE_TYPE_1_ROLECODE}")
    private String doCaseType1;

    @Value("${DO_CASE_TYPE_2_ROLECODE}")
    private String doCaseType2;

    @Value("${spring.profiles.active}")
    private String envProfile;

    @Value("${oa.taskUrl}")
    private String oaTaskUrl;

    @Value("${oa.projectName}")
    private String oaProjectName;

    @Value("${oa.key}")
    private String oaKey;

    @Value("${sccl.deployTo}")
    private String deployTo;
    @Value("${spring.profiles.active}")
    private String profilesActive;

    @Autowired
    private WfTaskMapper wfTaskMapper;
    @Autowired
    private IWfTaskHisService wfTaskHisService;
    @Lazy
    @Autowired
    private IWfProcInstService wfProcInstService;
    @Autowired
    private UserServiceImpl userService;
    @Autowired
    private IWfDoRecordService wfDoRecordService;
    @Autowired
    private IWfTaskAssigneeService wfTaskAssigneeService;
    @Autowired
    private IWfTaskAssigneeHisService wfTaskAssigneeHisService;

    @Autowired
    private IWfProcConfigService wfProcConfigService;
    @Autowired
    private IWfProcRecordService wfProcRecordService;
    @Autowired
    private WfSendoaLogMapper wfSendoaLogMapper;
    @Autowired
    private UserMapper userMapper;

    static String clientId = "CTSCNHF20170410";//由统一目录分配
    static String mobileclientId = "CTSCNHXT20210819";//由统一目录分配
    static String systemId = "1008";//表示各系统，取值由统一待办中心制定
    static String systemPassword = "B3!#dStosnLUwAbV";//表示各系统，取值由统一待办中心制定
    public static String url = "http://oaopenapi.paas.sc.ctc.com:8020/scportal_unitask/";//待办请求地址（不包括方法名）
    static String href = "http://172.16.47.127/login"; //连接打开的URL地址
    static String mobilehref = "https://m.sctel.com.cn/scnh/login";

    /**
     * 清除无效待办
     *
     * @param procInstId 流程实例ID,
     * @param taskId     任务ID
     * @param shardKey   分区字段
     * <AUTHOR>
     * @Date 2019/3/15 16:14
     */
    @Override
    public void deleteInvalidTask(String procInstId, String nodeId, String shardKey) {
        if (StringUtils.isBlank(procInstId) || StringUtils.isBlank(nodeId) || StringUtils.isBlank(shardKey)) {
            return;
        }
        Map<String, Object> param = new HashMap<>(4);
        param.put("procInstId", procInstId);
        param.put("nodeId", nodeId);
        param.put("shardKey", shardKey);
        if (logger.isDebugEnabled()) {
            logger.debug("exec deleteInvalidTask ....");
        }

        this.wfTaskMapper.deleteInvalidTask(param);
    }


    /**
     * 创建待办
     *
     * @param wfTask 待办对象
     * @return com.sccl.modules.unifloworacle.wftask.domain.WfTask
     * <AUTHOR>
     * @Date 2019/3/15 16:45
     */
    @Override
    public WfTask createTask(WfTask wfTask) throws Exception {
        //判断任务是否存在,任务存在 则返回
        WfTask wfTask1 = this.get(wfTask.getId(), wfTask.getShardKey());
        if (wfTask1 != null) {
            return wfTask;
        }
        WfTaskHis wfTaskHis = this.wfTaskHisService.get(wfTask.getId(), wfTask.getShardKey());
        if (wfTaskHis != null) {
            return wfTask;
        }
        boolean busiDeptCodeNotBlank = (wfTask.getBusiDeptCode() == null || "".equals(wfTask.getBusiDeptCode())) && wfTask.getCustomProps() != null && wfTask.getCustomProps().get("busiDeptCode") != null;
        if (busiDeptCodeNotBlank) {
            wfTask.setBusiDeptCode(wfTask.getCustomProps().get("busiDeptCode"));
        } else {
            User user = userService.selectUserByLoginId(wfTask.getOwnerId());
            if (user != null && user.getCompanies() != null) {
                //System.out.println(wfTask.getCompanies().toString());
                logger.info("wfTask.getStatus().toString()" + wfTask.getStatus().toString());
                logger.info("user.getCompanies()" + user.getCompanies());
/*                if (user.getCompanies().get(0).getId() != null&&user.getCompanies().get(0).getName() != null) {
                    wfTask.setBusiDeptCode(user.getCompanies().get(0).getId());
                    wfTask.setBusiDeptName(user.getCompanies().get(0).getName());
                }*/
            }
        }

        if ("true".equals(wfTask.getIsFirstNode())) {
            wfTask.setStatus(WFConstants.WT_STATUS_已处理);
        }
        System.out.println(wfTask.getStatus().toString());
        logger.info("wfTask.getStatus().toString()" + wfTask.getStatus().toString());
        if (WFConstants.WT_STATUS_已处理.equals(wfTask.getStatus())) {
            wfTask.setCompleteTime(new java.sql.Date(System.currentTimeMillis()));
        }

        if (wfTask.getCustomProps().get("operType") != null && wfTask.getCustomProps().get("operType").equals("-1")) {
            wfTask.setOwnerId(wfTask.getCustomProps().get("creatorLoginId"));
            wfTask.setEventType(WFConstants.WT_STATUS_退回拟稿);
        }

        //获取候选人
        List<WfTaskAssignee> assignees = dealNominee(wfTask);

        //将当前taskId刷到流程实例上
        WfProcInst wfprocinst = wfProcInstService.get(Long.valueOf(wfTask.getProcInstId()), wfTask.getShardKey());
        if (wfprocinst != null) {
            //从实例表中取出 IsSupHandle 标识 是否由系统后台调用生成
            wfTask.setIsSupHandle(wfprocinst.getIsSupHandle());
        }

        if (WFConstants.WT_STATUS_已处理.equals(wfTask.getStatus())) {
            // 使用moveTaskToHis方法，该方法内部已经处理了重复插入的问题
            this.wfTaskHisService.moveTaskToHis(wfTask);
            /*
             * 处理人不为空时，记录办理记录
             */
            if (StringUtils.isNotBlank(wfTask.getShardKey())
                    && StringUtils.isNotBlank(wfTask.getProcInstId())
                    && StringUtils.isNotBlank(wfTask.getOwnerId())) {
                wfDoRecordService.addWfDoRecordByWfTask(wfTask);
            }
        } else {

            this.insert(wfTask);

            if (assignees != null && assignees.size() > 0) {
                for (WfTaskAssignee assignee : assignees) {
                    assignee.setId(IdGenerator.getNextId());
                    assignee.setWfTaskId(wfTask.getId());
                    assignee.setBusiId(wfTask.getBusiId().toString());
                    assignee.setBusiAlias(wfTask.getBusiAlias());
                    assignee.setNodeId(wfTask.getNodeId());
                    long currTime = System.currentTimeMillis();
                    assignee.setCreateTime(new Timestamp(currTime));
                    assignee.setUpdateTime(new Timestamp(currTime));
                    if (assignee.getShardKey() == null) {
                        assignee.setShardKey(wfTask.getShardKey());
                    }
                    assignee.setWfTaskShardKey(wfTask.getShardKey());
                    //保存候选人信息
                    this.saveAssignee(wfTask, assignee);
                }
            }
        }
        boolean isFirstNode = ("true".equals(wfTask.getIsFirstNode()) && wfTask.getFitstNodeAutoSubmit()) || WFConstants.WT_STATUS_已处理.equals(wfTask.getStatus());
        if (isFirstNode) {
            WFModel wfModel = new WFModel();
            wfModel.setProcInstId(Long.valueOf(wfTask.getProcInstId()));
            wfModel.setProcTaskId(wfTask.getId());
            wfModel.setShardKey(wfTask.getShardKey());
            wfModel.setLoginId(wfTask.getOwnerId());
            Map<String, Object> map = new HashMap<String, Object>();
            map.put(WFConstants.VARIABLE_是否第一个节点, false);
            wfModel.setVariables(map);
            wfModel.setProcInstId(Long.valueOf(wfTask.getProcInstId()));
            wfModel.setNodeId(wfTask.getNodeId());
            wfModel.setNodeName(wfTask.getNodeName());
            this.completeTaskA(wfModel);
        }


        //在生成待办任务时，将上一环节的办理记录迁入历史表
        Map<String, Object> queryMap = new HashMap<>(4);
        queryMap.put("shardKey", wfTask.getShardKey());
        queryMap.put("procInstId", String.valueOf(wfTask.getProcInstId()));
        List<WfDoRecord> wfDoRecords = wfDoRecordService.selectBy(queryMap);

        if (wfDoRecords != null && !wfDoRecords.isEmpty()) {
            StringBuilder idStr = new StringBuilder();
            List<WfDoRecord> wfDoRecordHis = new ArrayList<>();
            for (WfDoRecord wfDoRecord : wfDoRecords) {
                wfDoRecord.setStatus(WFConstants.WPI_STATUS_完成);
                wfDoRecordHis.add(wfDoRecord);

                if (idStr.length() > 0) {
                    idStr.append(",");
                }
                idStr.append(wfDoRecord.getId());
            }
            wfDoRecordService.insertToRecordHis(wfDoRecordHis);

            wfDoRecordService.deleteByIdsDB(Convert.toStrArray(idStr.toString()));
        }

        return wfTask;
    }

    /**
     * 保存候选人
     *
     * @param wfTask   待办任务
     * @param assignee
     */
    private void saveAssignee(WfTask wfTask, WfTaskAssignee assignee) {
        if (WFConstants.WT_STATUS_已处理.equals(wfTask.getStatus())) {
            WfTaskAssigneeHis wfTaskAssigneeHis = new WfTaskAssigneeHis();
            BeanUtils.copyProperties(assignee, wfTaskAssigneeHis);
            this.wfTaskAssigneeHisService.insert(wfTaskAssigneeHis);
        } else {
            this.wfTaskAssigneeService.insert(assignee);
        }
    }

    /**
     * 完成任务(目前只有异步调用能实现功能，请勿使用同步)
     *
     * @param wfModel 流程对象
     * @return com.alibaba.fastjson.JSONObject
     */
    public JSONObject completeTaskA(WFModel wfModel) {
        JSONObject re = null;
        // 同步调用流程引擎
        /*if (isSyncCompleteTask) {
            re = postRestService(procInstUrl + "/completeTask", wfModel);
        } else {// 异步调用流程引擎
            wfModel.setMqRoutingKey(WFConstants.MQ_ROUTING_KEY_办理任务);

            re = sendToMQ(wfModel, WFConstants.MQ_ROUTING_KEY_流程引擎总队列);
        }*/
        re = postRestService(procInstUrl + "/completeTask", wfModel);
        return re;
    }

    /**
     * 同步发起一个请求
     *
     * @param url     请求地址
     * @param request 请求对象
     * @param param   其他参数
     * @return com.alibaba.fastjson.JSONObject
     */
    private JSONObject postRestService(String url, Object request, Object... param) {
        RestTemplate rt = new RestTemplate();
        JSONObject re = rt.postForObject(url, request, JSONObject.class, param);
        //re.put("success", WFConstants.成功);
        return re;
    }

    /**
     * 将请求信息放入MQ
     *
     * @param wfModel    流程对象
     * @param routingKey key
     * @return com.alibaba.fastjson.JSONObject
     */
    /*@Override
    public JSONObject sendToMQ(WFModel wfModel, String routingKey) {
        KafkaMessage mqMsg = new KafkaMessage();
        mqMsg.setExchange(WFConstants.MQ_EXCHANGE);
        mqMsg.setRoutingKey(routingKey);
        String json = JSONObject.toJSONString(wfModel);
        mqMsg.setPayload(json);
        JSONObject jsonObject = new JSONObject();
        try {
            kafkaProducer.sendMessage(mqMsg);
            jsonObject.put(WFConstants.SUCCESS, WFConstants.成功);
        } catch (Exception e) {
            jsonObject.put(WFConstants.SUCCESS, WFConstants.失败);
            logger.error("wftaskserviceimpl.sendToMQ 异常，原因：{}", e.getMessage());
        }
        return jsonObject;
    }*/

    /**
     * 获取候选人
     *
     * @param wfTask 待办任务
     * @return List<WfTaskAssignee>
     * <AUTHOR>
     * @Date 2019/3/16 13:08
     */
    public List<WfTaskAssignee> dealNominee(WfTask wfTask) {
        User user = null;
        //待办产生时，如果审批人不存在或者为空，产生到默认的角色下面去
        String bigDeptCode = wfTask.getCustomProps().get("busiDeptCode");
        if (StringUtils.isBlank(bigDeptCode)) {
            bigDeptCode = wfTask.getCustomProps().get("deptCode");
        }
        if (StringUtils.isBlank(bigDeptCode)) {
            WfProcInst procInst = wfProcInstService.get(Long.valueOf(wfTask.getProcInstId()), wfTask.getShardKey());
            if (procInst != null) {
                bigDeptCode = procInst.getBigDeptCode();
            }
        }
        if (StringUtils.isNotBlank(wfTask.getOwnerId()) && StringUtils.isBlank(wfTask.getCandidateRole())) {
            if (StringUtils.isNotBlank(bigDeptCode)) {
                user = userService.selectUserByLoginIdAndOrgCode(wfTask.getOwnerId(), bigDeptCode);
            }
        }

        Boolean flag = false;
        if (user != null) {
            flag = true;
            wfTask.setOwnerName(user.getName());
        }

        if (!flag) {
            if (StringUtils.isNotBlank(wfTask.getAssigneeId())) {
                String[] assignees = wfTask.getAssigneeId().split(",");
                for (String loginId : assignees) {
                    User user1 = userService.selectUserByLoginIdAndOrgCode(loginId, bigDeptCode);
                    if (user1 != null) {
                        flag = true;
                        break;
                    }
                }
            }
        }
        //找到用户后，就把角色清除掉
        if (flag) {
            wfTask.setCandidateRole(null);
            //未找到用户，并且角色为空，则产生到默认角色下
        } else if (!flag && StringUtils.isBlank(wfTask.getCandidateRole())) {
            String _doCaseType = wfTask.getCustomProps().get("BIZ_PROP_DO_CASE_TYPE");
            //依申请
            if ("1".equals(_doCaseType)) {
                wfTask.setCandidateRole(doCaseType1);
                //主动行使
            } else if ("2".equals(_doCaseType)) {
                wfTask.setCandidateRole(doCaseType2);
            } else {
                wfTask.setCandidateRole(doCaseType1 + "," + doCaseType2);
            }
        }

        List<WfTaskAssignee> list = this.wfTaskAssigneeService.getAssignee(wfTask);
        return list;
    }

    /**
     * 待办创建成功后的回调
     *
     * @param wfTask 待办任务
     * @return com.sccl.modules.uniflow.common.WFModel
     * <AUTHOR>
     * @Date 2019/3/17 11:39
     */
    @Override
    public WFModel taskCreatedCallback(WfTask wfTask) {
        WFModel wfModel = new WFModel();
        wfModel.setShardKey(wfTask.getShardKey());
        wfModel.setProcDefKey(wfTask.getProcDefId());
        wfModel.setBusiAlias(wfTask.getBusiAlias());
        wfModel.setBusiId(wfTask.getBusiId().toString());
        wfModel.setProcInstId(Long.valueOf(wfTask.getProcInstId() == null ? "0" : wfTask.getProcInstId()));
        wfModel.setProcTaskId(wfTask.getId());
        wfModel.setNodeId(wfTask.getNodeId());
        wfModel.setNodeName(wfTask.getNodeName());
        wfModel.setBusiTitle(wfTask.getTaskName());
        wfModel.setBusiType(wfTask.getBusiType());
        wfModel.setCallbackType(WFConstants.UNIFLOW_CALLBACK_任务创建);
        wfModel.setWfTask(wfTask);
        //组装扩展参数
        Map<String, Object> vars = new HashMap<>();
        if (wfTask.getCustomProps() != null) {
            wfTask.getCustomProps().forEach((k, v) -> {
                vars.put(k, v);
            });
        }
        wfModel.setVariables(vars);
        boolean success = WFConstants.失败;
        String message = null;
        Map<String, Object> map = null;
        JSONObject re = new JSONObject();
        try {
            re.put("wfModel", wfModel);
            success = WFConstants.成功;
        } catch (Exception e) {
            e.printStackTrace();
            message = "任务创建，回调业务模块出错:" + e.getMessage();
            success = WFConstants.失败;
            logger.debug(message);
        } finally {

            re.put("result", map);
            String mainInfo = "";
            if (success) {
                mainInfo = "任务创建后回调业务模块(taskCreatedCallback)|成功;业务侧返回:" + re.toJSONString();
            } else {
                mainInfo = "任务创建后回调业务模块(taskCreatedCallback)|失败;失败信息:" + message + ";业务侧返回:" + re.toJSONString();
            }
            WfOperLog log = new WfOperLog();
            log.setProcInstId(wfModel.getProcInstId() + "");
            log.setTaskId(wfModel.getProcTaskId() + "");
            log.setMainInfo(mainInfo);
            log.setOperTime(new java.util.Date());
            log.setShardKey(wfTask.getShardKey());
            String json = JSONObject.toJSONString(log);
            WorkflowUtil.sendMessageToMQ(json, WFConstants.MQ_ROUTING_KEY_流程操作日志);

        }
        re.put("success", success);
        re.put("message", message);
        return wfModel;
    }


    /**
     * 查询待办
     *
     * @param user  当前登录用户
     * @param query 查询条件
     * @return List<WfTask>
     * <AUTHOR>
     * @Date 2019/3/18 16:50
     */
    @Override
    public List<WfTaskDTO> queryTasks(User user, WfTaskQuery query) {
        /*query.setBusiAlias(org.apache.commons.lang.StringUtils.isBlank(query.getBusiAlias()) ? null : query.getBusiAlias());
        query.setOwnerId(user.getLoginId());
        query.setShardKey(user.getShardKey());
        query.setStatus(null);
        query.setRoleCode(getRoleCodes(user));
        query.setBusiDeptCode(user.getCompanies().get(0).getId());*/

        //admin可以查看所有的
        if (!ShiroUtils.getSubjct().hasRole(CommonConstants.ROLE_ADMIN)) {
            query.setBusiAlias(query.getBusiAlias() == null ? null : query.getBusiAlias());
            query.setOwnerId(user.getLoginId());
            query.setShardKey(user.getShardKey());

            query.setRoleCode(getRoleCodes(user));
            query.setBusiDeptCode(user.getCompanies().get(0).getId());
        } else
            query.setStatus(null);

        return wfTaskMapper.queryTasks4Affair(query);
    }


    /**
     * 通过流程实例ID删除办理记录
     *
     * @param procInstId 流程实例ID
     * @return 影响的行数
     * @createdBy:liuwenjun
     * @createaAt:2019年4月12日下午11:08:33
     */
    @Override
    public int deleteRecordByProcInstId(Long procInstId) {
        return wfTaskMapper.deleteRecordByProcInstId(procInstId);
    }

    /**
     * 通过流程实例ID删除待办任务
     *
     * @param procInstId 流程实例ID
     * @return 影响的行数
     * @createdBy:liuwenjun
     * @createaAt:2019年4月12日下午11:08:33
     */
    @Override
    public int deleteTaskByProcInstId(Long procInstId) {
        return wfTaskMapper.deleteTaskByProcInstId(procInstId);
    }


    /**
     * 获取用户的所有角色
     *
     * @param user 用户
     * @return 所有角色编码 逗号分隔
     */
    private String getRoleCodes(User user) {
        List<String> roleCodes = new ArrayList<String>();
        roleCodes.add(user.getLoginId());
        if (user.getRoleCodes() != null && user.getRoleCodes().length > 0) {
            for (String code : user.getRoleCodes()) {
                if (StringUtils.isBlank(code)) {
                    roleCodes.add(code);
                }
            }
        }
        return StringUtil.join(roleCodes, ",");
    }

    /**
     * 完成任务
     *
     * @param user  用户
     * @param param 审批时的对象
     * @return JSONObject
     */
    @Override
    public JSONObject completeTask(User user, Map<String, Object> param) throws Exception {
        JSONObject re = WorkflowUtil.validateParam(param, "procTaskId");
        if (!re.getBoolean(WFConstants.SUCCESS)) {
            throw new UniflowException(re.getString("message"));

        }

        Object nextNodeId = param.get(WFConstants.NEXT_NODE_ID);
        if (StringUtils.isNotNull(nextNodeId) && !nextNodeId.toString().equals(WFConstants.NEXT_NODE_送结束)) {
            Object nextNodeUserId = param.get("nextNodeUserId");
            if (null == nextNodeUserId || StringUtils.isEmpty(nextNodeUserId.toString())) {
                throw new UniflowException("流程提交失败，未获取到下一节点审批人信息");
            }
            User dbUser = this.userService.get(Long.valueOf(nextNodeUserId.toString()));
            param.put(WFConstants.VARIABLE_指定审批用户, dbUser.getLoginId());
        }


        WFModel wfModel = new WFModel();
        wfModel.setLoginId(user.getLoginId());
        wfModel.setShardKey(param.get("shardKey").toString());
        wfModel.setProcTaskId(Long.valueOf(param.get("procTaskId").toString()));

        param.put("handlerName", user.getUserName());
        param.put("deptCode", user.getDepartments().get(0).getId());
        param.put("deptName", user.getDepartments().get(0).getName());
        //当存在 多条分支时 通过流程图上定义的条件决定 走哪条线
        if (param.get("branchCondition") != null && StringUtils.isNotBlank(param.get("branchCondition").toString())) {
            String branchCondition = param.get("branchCondition").toString();
            String[] branchConditions = branchCondition.split("==");
            if (branchConditions.length == 2) {
                param.put(branchConditions[0].replace("${", ""), branchConditions[1].replace("}", ""));
            }
        }
        wfModel.setVariables(param);


        String mainInfo = "";
        WfOperLog log = new WfOperLog();
        log.setProcInstId(wfModel.getProcInstId() + "");
        log.setTaskId(wfModel.getProcTaskId() + "");
        log.setOperTime(new java.util.Date());
        log.setShardKey(wfModel.getShardKey());
        try {
            re = this.executeCompleteTask(wfModel);
            mainInfo = "WfTaskServiceImpl.completeTask调用成功";

        } catch (Exception ex) {
            logger.error(ex.getMessage());
            mainInfo = "WfTaskServiceImpl.completeTask调用失败:" + ex.getMessage() + ";";
            throw ex;
        } finally {
            log.setMainInfo(mainInfo);
            String json = JSONObject.toJSONString(log);
            //WorkflowUtil.sendMessageToMQ(json, WFConstants.MQ_ROUTING_KEY_流程操作日志);
        }
        return re;
    }

    /**
     * 待办推送到OA
     *
     * @param wfTask
     * @param wfModel
     * @param vars
     * @param nextNodeId
     */
    private void wfSendOA(WfTask wfTask, WFModel wfModel, Map<String, Object> vars, Object nextNodeId) {
        try {
            //辽宁给OA推送待办 添加逻辑判断 如果是送结束就不推送待办
            if (StringUtils.isNotNull(nextNodeId) && !nextNodeId.toString().equals(WFConstants.NEXT_NODE_送结束)) {
                if ("ln".equals(deployTo)) {
                    // dev环境不推送OA
                    if (!profilesActive.contains("dev")) {
                        sendTask2OA(wfTask, vars, wfModel.getLoginId());
                    }
                    // } else if (("sc").equals(deployTo) && "sc".equals(profilesActive)) {
                } else {
                    // 送代办
                    WfSendoaLog wflog = new WfSendoaLog();
                    //ScOaServiceUtil.sendTask2SCOA(wfTask, wflog, vars.get("appointUserId").toString(), "1");
                    //ScOaTaskUtil.sendTask(wfTask, wflog,vars.get("appointUserId").toString());
                    sendTask(wfTask, wflog, vars.get("appointUserId").toString());
                    wflog.getStatus();
                    //wfSendoaLogMapper.insert(wflog);// 插入日志
                    // 已完成节点送结束
                    WfTaskHis wth = new WfTaskHis();
                    wth.setProcInstId(wfTask.getProcInstId());
                    List<WfTaskHis> wfTaskHis = wfTaskHisService.selectList(wth);
                    if (wfTaskHis != null && wfTaskHis.size() > 0) {//第一个节点没送代办 不送结束
                        for (WfTaskHis his : wfTaskHis) {
                            if (!"11".equals(his.getRemark())) {
                                wfTask.setOwnerId(his.getOwnerId());
                                //ScOaServiceUtil.sendTask2SCOA(wfTask, wflog, his.getOwnerId(), "5");
                                //ScOaTaskUtil.sendUpdateTask(his,wflog);
                                sendUpdateTask(his, wflog);
                                //wfSendoaLogMapper.insert(wflog);// 插入日志
                                WfTaskHis hisu = new WfTaskHis();
                                hisu.setId(his.getId());
                                hisu.setRemark("11");//;已经推送结束
                                wfTaskHisService.updateForModel(hisu);
                            }
                        }
                    }
                }
            }
            // 送结束 给四川OA
            if (StringUtils.isNotNull(nextNodeId) && nextNodeId.toString().equals(WFConstants.NEXT_NODE_送结束)) {
                //if (("sc").equals(deployTo) && "sc".equals(profilesActive)) {
                if (("sc").equals(deployTo)) {
                    WfSendoaLog wflog = new WfSendoaLog();
                    WfTaskHis wth = new WfTaskHis();
                    wth.setProcInstId(wfTask.getProcInstId());
                    List<WfTaskHis> wfTaskHis = wfTaskHisService.selectList(wth);
                    if (wfTaskHis != null && wfTaskHis.size() > 0) {//所有的 历史代办送一次结束 ，避免代办未结束
                        for (WfTaskHis his : wfTaskHis) {
                            /*if (!"11".equals(his.getRemark()))*/ {
                                wfTask.setOwnerId(his.getOwnerId());
                                //ScOaServiceUtil.sendTask2SCOA(wfTask, wflog, his.getOwnerId(), "5");
                                //ScOaTaskUtil.sendUpdateTask(his,wflog);
                                sendUpdateTask(his, wflog);
                                // wfSendoaLogMapper.insert(wflog);// 插入日志
                                WfTaskHis hisu = new WfTaskHis();
                                hisu.setId(his.getId());
                                hisu.setRemark("11");//;已经推送结束
                                wfTaskHisService.updateForModel(hisu);
                            }
                        }
                    } else {
                        //ScOaServiceUtil.sendTask2SCOA(wfTask, wflog, wfModel.getLoginId(), "5");
                        //ScOaTaskUtil.sendUpdateTask(wfTaskHis,wflog);
                        wfSendoaLogMapper.insert(wflog);// 插入日志
                    }
                } else {
                    // 辽宁 送结束 VARIABLE_指定审批用户 为空
                    if (!profilesActive.contains("dev")) {
                        sendTask2OA(wfTask, vars, wfModel.getLoginId());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 最终调用 PI执行任务完成方法，
     *
     * @param wfModel 流程对象
     * @return JSONObject
     */
    private JSONObject executeCompleteTask(WFModel wfModel) throws Exception {
        JSONObject re = new JSONObject();
        // 提交前的准备
        Map<String, Object> vars = wfModel.getVariables();
        String advice = "";
        String handlerName = "";
        Object nextNodeId = null;
        Object firstNode = null;
        if (vars != null) {
            handlerName = (String) vars.get("handlerName");
            advice = (String) vars.get("advice");
            nextNodeId = vars.get(WFConstants.NEXT_NODE_ID);
            firstNode = vars.get(WFConstants.VARIABLE_是否第一个节点);
        }
        WfTask wfTask = this.get(wfModel.getProcTaskId(), wfModel.getShardKey());
        if (wfTask != null) {
            wfTask.setStatus(WFConstants.WT_STATUS_已处理);
            wfTask.setOwnerId(wfModel.getLoginId());
            wfTask.setOwnerName(handlerName);
            wfTask.setCompleteTime(new java.sql.Date(System.currentTimeMillis()));

            wfModel.setProcInstId(Long.valueOf(wfTask.getProcInstId()));
            wfModel.setNodeId(wfTask.getNodeId());
            wfModel.setNodeName(wfTask.getNodeName());
            if (wfModel.getBusiId() == null)
                wfModel.setBusiId(wfTask.getBusiId().toString());
            if (wfModel.getVariables() != null) {
                wfModel.getVariables().put("parentTaskId", wfTask.getId());
                //拦截审批意见，不需要将审批意见传入下一个节点
                wfModel.getVariables().remove("advice");
            }
            //设置审批意见
            if (StringUtils.isNotBlank(advice)) {
                wfTask.setCustomProps(new HashMap<String, String>());
                wfTask.getCustomProps().put("advice", advice);
                //wfTask.setCustomProps(wfTask.getCustomProps());
            }

            //如果是首节点的提交。先回调业务的uniflowCallBack方法 ，当业务的方法异常时则不调用PI
            if (StringUtils.isNotNull(firstNode) && firstNode.toString().equalsIgnoreCase("true")) {
                WFModel callBackWfModel = new WFModel();
                BeanUtils.copyProperties(wfModel, callBackWfModel);
                callBackWfModel.setCallbackType(WFConstants.UNIFLOW_CALLBACK_流程创建);
                callBackWfModel.setBusiAlias(wfTask.getBusiAlias());
                callBackWfModel.setBusiId(wfTask.getBusiId().toString());
                callBackWfModel.setApplyUserId(wfModel.getLoginId());
                wfProcInstService.uniflowCallBack(callBackWfModel);
            }

            //如果下一环节为送结束，则先回调业务的uniflowCallBack方法 ，当业务的方法异常时则不调用PI
            if (StringUtils.isNotNull(nextNodeId) && nextNodeId.toString().equals(WFConstants.NEXT_NODE_送结束)) {
                WFModel callBackWfModel = new WFModel();
                BeanUtils.copyProperties(wfModel, callBackWfModel);
                callBackWfModel.setCallbackType(WFConstants.UNIFLOW_CALLBACK_流程完成);
                callBackWfModel.setBusiAlias(wfTask.getBusiAlias());
                callBackWfModel.setBusiId(wfTask.getBusiId().toString());
                callBackWfModel.setApplyUserId(vars.get("applyUserId").toString());
                wfProcInstService.uniflowCallBack(callBackWfModel);
            }

            //流程中的节点调用  判断如果既不是初始节点 又不是 最后一个节点 那么就是中间的节点
            if (!(StringUtils.isNotNull(firstNode) && firstNode.toString().equalsIgnoreCase("true"))
                    && !(StringUtils.isNotNull(nextNodeId) && nextNodeId.toString().equals(WFConstants.NEXT_NODE_送结束))) {
                WFModel callBackWfModel = new WFModel();
                BeanUtils.copyProperties(wfModel, callBackWfModel);
                callBackWfModel.setCallbackType(WFConstants.UNIFLOW_MID_NODE);
                callBackWfModel.setBusiAlias(wfTask.getBusiAlias());
                callBackWfModel.setBusiId(wfTask.getBusiId().toString());
                callBackWfModel.setApplyUserId(vars.get("applyUserId").toString());
                wfProcInstService.uniflowCallBack(callBackWfModel);
            }

            //保存审批记录
            saveWfProcRecord(wfTask, WFConstants.processActionEnum.审批通过.getValue());

            //待办迁移到历史表
            moveTaskToHis(wfTask);

            // 调用流程引擎
            JSONObject se = completeTaskA(wfModel);
            //System.out.println(wfTask.getStatus().toString());
            //logger.info("WFConstants.JSON_SUCCESS_KEY  BusiId:" + wfTask.getBusiId().toString() + "-" + WFConstants.JSON_SUCCESS_KEY.toString());
            if (se.getBooleanValue(WFConstants.JSON_SUCCESS_KEY)) {
                //logger.info("processLog 完成任务成功 busi_id:{} 参数:{} 返回:{}", wfTask.getBusiId().toString(), JSONObject.toJSONString(wfModel), se.toJSONString());
                //处理原来通过kafka发送的回调 逻辑有多 懒得去理了 直接调用原方法
                WFModel callBackWfModel = JSONObject.parseObject(se.getString(WFConstants.WF_MODEL_KEY), WFModel.class);
                try {
                    //if(!"EndNoneEvent".equals(wfModel.getVariables().get("nodeId").toString()))
                    wfProcInstService.workflowSetMQNew(callBackWfModel);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new UniflowException("启流成功,创建待办失败:" + e.getMessage());
                }
            } else
                throw new UniflowException(se.getString(WFConstants.JSON_MESSAGE_KEY));

            //推送给OA
            try {
                wfSendOA(wfTask, wfModel, vars, nextNodeId);
                logger.info("processLog 发送OA成功busi_id:{} ", wfTask.getBusiId().toString());
            } catch (Exception e) {
                e.printStackTrace();
                logger.error("推送OA待办失败 busiId======={},taskId====={},proc_inst_id======={}", wfTask.getBusiId(), wfTask.getId(), wfTask.getProcInstId());
            }

        }

        return re;
    }

    /**
     * 辽宁调使用webservice给OA推送待办
     *
     * @param loginId
     * @param procInstId 流程实例ID
     * @param title      流程名称
     * @param handlerId  代办人loginId1
     */
    @Override
    public void sendTask2OA(WfTask wfTask, Map<String, Object> param, String loginId) {
        //拼条件
        System.out.println(oaTaskUrl);
        System.out.println(oaProjectName);
        System.out.println(oaKey);
        String targets = param.get(WFConstants.VARIABLE_指定审批用户) == null ? "" : param.get(WFConstants.VARIABLE_指定审批用户).toString();
        System.out.println(targets);
        String ownerId = wfTask.getOwnerId() == null ? "" : wfTask.getOwnerId();
        System.out.println(ownerId);
        String applyUserId = param.get("applyUserId") == null ? "" : param.get("applyUserId").toString();
        System.out.println(applyUserId);

        if (envProfile.contains("ln")) {//判断生产环境才推送待办 避免测试环境不能使用流程的情况
            String url = oaTaskUrl;
            String httpMethod = "POST";
            String soapAction = "";
            String callMethod = "update";
            String targetNameSpace = "http://processtask.service.webservice.unifiedworkbench.zoneland.net";
            String prefix = "proc";
            String time = "10080";
            String defaultLink = PropertiesUtils.getInstance().getProperty("oa.oaLinkUrl");
            String[] argNames = new String[]{
                    "arg0",//接入项目名称,由事前约定
                    "arg1",//接入项目密码,由事前约定
                    "arg2",//待办人,多值通过”;”区分,可以接受rdn,dn或者LDAP中其他唯一标识符
                    "arg3",//办理人,单值, 可以接受rdn,dn或者LDAP中其他唯一标识符
                    "arg4",//发起人,单值, 可以接受rdn,dn或者LDAP中其他唯一标识符
                    "arg5",//流程意见
                    "arg6",//标题,不能为空
                    "arg7",//任务的唯一标识符,系统通过此来判断是否是同一个任务的待办.
                    "arg8",//扩充的唯一标识符,由于拆分等特殊情况下使用
                    "arg9",//特征字串,用于标识模块
                    "arg10",//保留字段,无作用
                    "arg11",//任务到达时间
                    "arg12",//流程名称
                    "arg13",//文件类型
                    "arg14",//活动节点
                    "arg15",//是否是第一个活动
                    "arg16",//分类
                    "arg17",//紧急度
                    "arg18",//密级
                    "arg19",//文号
                    "arg20",//办理时限,自到达时间至办理截止时间的工作分钟数
                    "arg21",//办理截止时间
                    "arg22",//是否启用催办
                    "arg23",//自截止时间到催办时间的工作分钟数
                    "arg24",//
                    "arg25",//是否启用提醒
                    "arg26",//是否强制提醒
                    "arg27",//链接
                    "arg28",//查看链接
                    "arg29",//办理链接
                    "arg30",//删除链接
                    "arg31",//督办链接
                    "arg32",//挂起链接
                    "arg33",//重置链接
                    "arg34"//调度链接
            };
            String[] values = new String[]{
                    oaProjectName,//接入项目名称,由事前约定
                    oaKey,//接入项目密码,由事前约定
                    targets,//待办人,多值通过”;”区分,可以接受rdn,dn或者LDAP中其他唯一标识符
                    ownerId,//办理人,单值, 可以接受rdn,dn或者LDAP中其他唯一标识符
                    applyUserId,//发起人,单值, 可以接受rdn,dn或者LDAP中其他唯一标识符
                    "",//流程意见
                    wfTask.getTaskName(),//标题,不能为空
                    wfTask.getProcInstId(),//任务的唯一标识符,系统通过此来判断是否是同一个任务的待办.
                    "",//扩充的唯一标识符,由于拆分等特殊情况下使用
                    "",//特征字串,用于标识模块
                    "",//保留字段,无作用
                    "",//任务到达时间
                    "",//流程名称
                    "",//文件类型
                    "",//活动节点
                    "false",//是否是第一个活动
                    "",//分类
                    "",//紧急度
                    "",//密级
                    "",//文号
                    time,//办理时限,自到达时间至办理截止时间的工作分钟数
                    "",//办理截止时间
                    "false",//是否启用催办
                    time,//自截止时间到催办时间的工作分钟数
                    "",//hastenTime Calendar
                    "false",//是否启用提醒
                    "false",//是否强制提醒
                    defaultLink,//链接
                    "",//查看链接
                    "",//办理链接
                    "",//删除链接
                    "",//督办链接
                    "",//挂起链接
                    "",//重置链接
                    ""//调度链接
            };
            String oaXml = WebServiceClient.callWebService(url,
                    httpMethod,
                    soapAction,
                    targetNameSpace,
                    prefix,
                    callMethod,
                    argNames,
                    values
            );
            System.out.println(oaXml);
            try {
                WfSendoaLog wflog = new WfSendoaLog();
                wflog.setProcinstid(wfTask.getProcInstId());
                wflog.setSendxml(Arrays.toString(values));
                wflog.setReturnxml(oaXml);
                wflog.setStatus("1");
                wflog.setCreatetime(new Date());
                wfSendoaLogMapper.insert(wflog);// 插入日志
            } catch (Exception e) {
            }
        }
    }

    private static SyncUnitaskVO getSyncUnitaskVO() {
        SyncUnitaskVO syncUnitaskVO = new SyncUnitaskVO();
        String timeStamp = String.valueOf(System.currentTimeMillis());
        String keys = Md5Util.md5Encoder(systemId + "$" + timeStamp + "$" + systemPassword);
        syncUnitaskVO.setClientId(clientId);
        //syncUnitaskVO.setMobileClientId("");
        syncUnitaskVO.setKeys(keys);
        syncUnitaskVO.setSystemId(systemId);
        syncUnitaskVO.setTimeStamp(timeStamp);
        return syncUnitaskVO;
    }

    /**
     * 获得当前日期，例如2009-09-04 22:22:22
     *
     * @return
     */
    private static final String getNowDateTime() {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            TimeZone zone = TimeZone.getTimeZone("GMT+8"); // 获取中国时区
            TimeZone.setDefault(zone); // 设置时区

            return formatter.format(new Date());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return null;
    }
    private static final String DateToStr(Date date) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            TimeZone zone = TimeZone.getTimeZone("GMT+8"); // 获取中国时区
            TimeZone.setDefault(zone); // 设置时区

            return formatter.format(date);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void sendTask(WfTask wfTask, WfSendoaLog wflog, String owner) {
        SyncUnitaskVO syncUnitaskVO = getSyncUnitaskVO();
        List<SyncUnitaskVO.UnitaskVO> listUnitaskVO = new ArrayList<SyncUnitaskVO.UnitaskVO>();
        SyncUnitaskVO.UnitaskVO unitaskVO = new SyncUnitaskVO.UnitaskVO();
        //待办在专业系统的创建时间，不是请求时间，OA根据这个时间进行分表
        //测试去当前时间，专业系统根据自己系统做修改

        //unitaskVO.setCreateDate(getNowDateTime());//"消除待办要传这个时间，OA分表或者传null";
        unitaskVO.setCreateDate(DateToStr(wfTask.getCompleteTime()));
        System.out.println("wfTask.getGenTime():"+DateToStr(wfTask.getGenTime()));
        System.out.println("wfTask.getCompleteTime():"+DateToStr(wfTask.getCompleteTime()));
        //unitaskVO.setCreateDate(wfTask.getGenTime());
        unitaskVO.setHref(href);
        unitaskVO.setDoneHref(href);
        unitaskVO.setMobileHref(mobilehref);
        unitaskVO.setDoneMobileHref(mobilehref);
        unitaskVO.setMessageContent(wfTask.getTaskName());
        unitaskVO.setOwner(owner);
        unitaskVO.setPushMessage(1);//是否推送消息【1：是；0：否】
        unitaskVO.setSender(wfTask.getOwnerId());
        unitaskVO.setUniid(wfTask.getProcInstId());
        unitaskVO.setSenderName(wfTask.getOwnerName());
        unitaskVO.setTitle(wfTask.getTaskName());
        unitaskVO.setTaskType("10000");
        //unitaskVO.setActivityCode(wfTask.getNodeId());
        //unitaskVO.setActivityName(wfTask.getNodeName());
        unitaskVO.setBussinessName("");//可以为空
        //unitaskVO.setBussinessCode(wfTask.getBusiAlias());//可以为空
        unitaskVO.setCodeLetter("");//"文号", 待阅、已阅的文号【可以为空】
        unitaskVO.setRemark("");//备注，待扩展【可以为空】
        listUnitaskVO.add(unitaskVO);
        syncUnitaskVO.setMobileClientId(mobileclientId);
        syncUnitaskVO.setListUnitask(listUnitaskVO);
        System.out.println(JSON.toJSONString(syncUnitaskVO));
        doPostByMethod(wfTask.getId().toString(), wflog, syncUnitaskVO, "sendNewUnitask");
    }

    @Override
    public void sendUpdateTask(WfTaskHis wfTaskHis, WfSendoaLog wflog) {
        SyncUnitaskVO syncUnitaskVO = getSyncUnitaskVO();
        //请求 地址和方法名
        List<SyncUnitaskVO.UnitaskVO> listUnitaskVO = new ArrayList<SyncUnitaskVO.UnitaskVO>();
        SyncUnitaskVO.UnitaskVO unitaskVO = new SyncUnitaskVO.UnitaskVO();
        //unitaskVO.setActivityCode(wfTaskHis.getNodeId());
        //待办在专业系统的创建时间，不是请求时间，OA根据这个时间进行分表
        //测试去当前时间，专业系统根据自己系统做修改
        //unitaskVO.setCreateDate(wfTaskHis.getGenTime());
        //unitaskVO.setCreateDate(getNowDateTime());
        System.out.println("wfTaskHis.getGenTime():"+DateToStr(wfTaskHis.getGenTime()));
        System.out.println("wfTaskHis.getCompleteTime():"+DateToStr(wfTaskHis.getCompleteTime()));
        unitaskVO.setCreateDate(DateToStr(wfTaskHis.getGenTime()));
        unitaskVO.setOwner(wfTaskHis.getOwnerId());
        //unitaskVO.setOwner("18981952312");
        unitaskVO.setUniid(wfTaskHis.getProcInstId());
        //unitaskVO.setUniid("1234");
        unitaskVO.setDelFlag(1);//delFlag与status必须保留一个值
        unitaskVO.setStatus(1);
        listUnitaskVO.add(unitaskVO);
        syncUnitaskVO.setListUnitask(listUnitaskVO);
        System.out.println(JSON.toJSONString(syncUnitaskVO));
        doPostByMethod(wfTaskHis.getId().toString(), wflog, syncUnitaskVO, "sendUpdateUniTask");
    }

    @Override
    public void doPostByMethod(String unid, WfSendoaLog wflog, SyncUnitaskVO syncUnitaskVO, String method) {
        //请求 地址和方法名
        try {
            HttpClientUtil util = new HttpClientUtil(url + method);
            util.setBodyData(JSON.toJSONString(syncUnitaskVO));
            util.post();
            String content = util.getContent();
            wflog.setStatus(util.getStatusCode() + "");
            wflog.setProcinstid(unid);
            wflog.setReturnxml(content);
            wflog.setSendxml(JSON.toJSONString(syncUnitaskVO));
            wflog.setCreatetime(new Date());
            wfSendoaLogMapper.insert(wflog);// 插入日志
        } catch (Exception e) {
            logger.error("流程发送OA待办异常，原因：{}", e.getMessage());
            //违反唯一约束的时候 报错，允许报错
            //e.printStackTrace();
            wflog.setProcinstid(unid);
            wflog.setReturnxml(e.getMessage());
            wflog.setSendxml(JSON.toJSONString(syncUnitaskVO));
            wflog.setCreatetime(new Date());
            wfSendoaLogMapper.insert(wflog);// 插入日志

        }

    }

    /**
     * 迁移任务到已办表
     *
     * @param wfTask 待办任务
     */
    @Override
    public void moveTaskToHis(WfTask wfTask) {
        //迁移到已办表
        this.wfTaskHisService.moveTaskToHis(wfTask);

        if (StringUtils.isNotBlank(wfTask.getShardKey())
                && StringUtils.isNotBlank(wfTask.getProcInstId())
                && StringUtils.isNotBlank(wfTask.getOwnerId())) {
            this.wfDoRecordService.addWfDoRecordByWfTask(wfTask);
        }

        //删除待办表
        this.deleteDB(wfTask.getId(), wfTask.getShardKey());

        Map<String, Object> deleteMap = new HashMap<>(2);
        deleteMap.put("wfTaskId", wfTask.getId());
        deleteMap.put("shardKey", wfTask.getShardKey());
        //删除候选人表
        this.wfTaskAssigneeService.deleteAssigneeByMap(deleteMap);

    }


    /**
     * 获取流程图
     *
     * @param procInstId 流程实例ID
     * @return
     */
    @Override
    public InputStream diagramTrack(Long procInstId) throws Exception {
        String url = procInstUrl + "/diagramTrack?processInstanceId=" + procInstId;
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<Resource> entity = restTemplate.getForEntity(url, Resource.class);
        return entity.getBody().getInputStream();
    }

    /**
     * 获取任务
     *
     * @param procTaskId
     * @param shardKey
     * @param loginId
     * @param handlerName
     * @return WfTask
     */
    @Override
    public WfTask beforeCompleteTask(Long procTaskId, String shardKey, String loginId, String handlerName) throws UniflowException {
        WfTask wfTask = this.get(procTaskId, shardKey);
        if (wfTask == null) {
            throw new UniflowException("任务不存在");
        }

        wfTask.setStatus(WFConstants.WT_STATUS_已处理);
        wfTask.setOwnerId(loginId);
        wfTask.setOwnerName(handlerName);
        wfTask.setCompleteTime(new java.sql.Date(System.currentTimeMillis()));
        return wfTask;
    }

    /**
     * 流程退回失败时的回滚
     *
     * @param procTaskId 任务ID
     * @param shardKey   分片字段
     * @return WfTask
     */
    @Override
    public WfTask rollbackTask(Long procTaskId, String shardKey) throws UniflowException {
        WfTask wfTask = this.get(procTaskId, shardKey);
        if (wfTask == null) {
            throw new UniflowException("任务不存在");
        }

        wfTask.setStatus(WFConstants.WT_STATUS_未处理);
        wfTask.setCompleteTime(null);
        this.update(wfTask);
        return wfTask;
    }

    /**
     * 执行打开待办任务
     *
     * @param task 待办对象
     * @return JSONObject
     */
    @Override
    public JSONObject executeTask(WfTask task) throws UniflowException {
        String isMutilTask = "";
        String isSequential = "";

        if (task.getCustomProps() != null) {
            isMutilTask = task.getCustomProps().get(WFConstants.VARIABLE_是否多实例);
            isSequential = task.getCustomProps().get(WFConstants.VARIABLE_是否为串行);
        } else {
            isMutilTask = "false";
            isSequential = "";
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(WFConstants.VARIABLE_是否多实例, isMutilTask);
        jsonObject.put(WFConstants.VARIABLE_是否为串行, isSequential);

        if (task != null) {
            //读取业务模块的相关信息(查看/编辑URL)
            WfProcConfig wfProcConfig = this.wfProcConfigService.getWfProcConfigByBusiAlias(task.getBusiAlias());
            if (wfProcConfig != null) {
                if (WFConstants.BUSI_TYPE_拟稿节点.equalsIgnoreCase(task.getBusiType())) {
                    jsonObject.put("busiAliasUrl", wfProcConfig.getEditUrl());
                    jsonObject.put("firstNode", Boolean.TRUE);
                } else {
                    jsonObject.put("busiAliasUrl", wfProcConfig.getViewUrl());
                    jsonObject.put("firstNode", Boolean.FALSE);
                }
                jsonObject.put("viewBusiUrl", wfProcConfig.getViewUrl());
                jsonObject.put("busiAliasId", task.getBusiId());
                jsonObject.put("procModelId", task.getProcModelId());
            } else {
                throw new UniflowException("未找到流程配置，请联系管理员");
            }
            //从实例表中获取是否由系统后台生成的待办
            WfProcInst wfProcInst = this.wfProcInstService.get(Long.parseLong(task.getProcInstId()));
            if (wfProcInst != null) {
                jsonObject.put("isSupHandle", wfProcInst.getIsSupHandle());
                jsonObject.put("applyUserId", wfProcInst.getApplyUserId() != null ? String.valueOf(wfProcInst.getApplyUserId()) : "");
            }
            try {
                Long proCreate = wfProcInst.getCreatorId();
                User user = userMapper.selectById(proCreate.toString());
                String CreatorName = wfProcInst.getCreatorName();
                jsonObject.put("creatorid", user.getLoginId());
                jsonObject.put("creatorname", CreatorName);
            } catch (Exception e) {
                e.printStackTrace();
            }
            //查询下一节点信息
            JSONObject taskInfo = this.getNextNodeInfo(task);
            jsonObject.put("taskInfo", taskInfo);
            jsonObject.put("eventType", task.getEventType());
            //四川版本判断是否是省公司单子
            if ("sc".equals(PropertiesUtils.getInstance().getProperty("sccl.deployTo"))) {
                try {
                    boolean isProvinceBill = scIsProvinceBill(task.getProcInstId());
                    jsonObject.put("isProvinceBill", isProvinceBill);
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }

        }

        return jsonObject;
    }

    private boolean scIsProvinceBill(String proinstId) {
        boolean bv = false;
        List<Map<String, Object>> list = wfTaskMapper.scGetBillRange(proinstId);
        if (list != null && list.size() > 0)
            bv = true;
        return bv;
    }


    /**
     * 获取当前节点的下一环节信息
     *
     * @param task 待办任务
     * @return JSONObject
     * @throws UniflowException
     */
    private JSONObject getNextNodeInfo(WfTask task) throws UniflowException {
        String url = procInstUrl + "/getNextTaskInfoNew/" + task.getProcInstId() + "/" + task.getId();
        JSONObject jsonObject = RestTemplateUtil.getRestService(url);
        if (jsonObject == null || !jsonObject.getBoolean(WFConstants.SUCCESS)) {
            logger.error("获取下一节点信息异常，原因：{}", jsonObject.getString("message"));
            throw new UniflowException("加载流程错误，请联系支撑人员！(错误原因：未获取到下一环节审批信息)");
        }

        JSONArray taskinfoArray = jsonObject.getJSONArray("taskInfos");
        if (task.getBusiAlias().equals("ADD_SITEREADING")&&task.getNodeName().equals("现场抄表")) {
            WfProcInst wfProcInst = this.wfProcInstService.get(Long.parseLong(task.getProcInstId()));
            Long   proCreate =wfProcInst.getCreatorId();
            User user=userMapper.selectById(proCreate.toString());
            for (int i = 0; i < taskinfoArray.size(); i++) {
                // 遍历 jsonarray 数组，把每个对象转成 json 对象
                JSONObject insertObj = taskinfoArray.getJSONObject(i);
                try {
                    String candidateUserIdExpression = insertObj.getString("candidateUserIdExpression");
                    insertObj.put("candidateUserIdExpression", user.getLoginId());
                } catch (Exception e) {

                }

            }
        }
        JSONObject taskinfo = new JSONObject();
        JSONArray ja = new JSONArray();
        for (int i = 0; i < taskinfoArray.size(); i++) {
            JSONObject jo = taskinfoArray.getJSONObject(i);
            String candidateUserIdExpression = jo.getString("candidateUserIdExpression");
            //判断是否有下一步审批人
            if (StringUtils.isNotBlank(candidateUserIdExpression)) {
                jo.put("candidateUserNameExpression", this.wfProcInstService.getNextNodeUserInfo(candidateUserIdExpression));
            }
            ja.add(jo);
        }
        taskinfo.put("taskInfos", ja);

        return taskinfo;
    }


    /**
     * 在提交完成后保存提交记录
     *
     * @param wfTask     流程对象
     * @param actionCode 操作码
     */
    @Override
    public void saveWfProcRecord(WfTask wfTask, String actionCode) {
        WfProcRecord wfProcRecord = new WfProcRecord();

        wfProcRecord.setActionCode(actionCode);
        wfProcRecord.setBusiId(wfTask.getBusiId());
        wfProcRecord.setBusiType(wfTask.getBusiAlias());
        if (wfTask.getCustomProps() != null) {
            wfProcRecord.setAdvice(wfTask.getCustomProps().get("advice"));
        }
        wfProcRecord.setNodeId(wfTask.getNodeId());
        wfProcRecord.setNodeName(wfTask.getNodeName());
        wfProcRecord.setOperateUser(wfTask.getOwnerId());
        wfProcRecord.setOperateUserName(wfTask.getOwnerName());
        wfProcRecord.setProcInstId(wfTask.getProcInstId());
        wfProcRecord.setShardKey(wfTask.getShardKey());
        wfProcRecord.setWfTaskShardKey(wfTask.getAreaCode());

        this.wfProcRecordService.insert(wfProcRecord);

    }
/*    public String getInitiator(String PROC_INST_ID_) {
        HistoricProcessInstance hip = wfTaskHisService.createHistoricProcessInstanceQuery().processInstanceId(PROC_INST_ID_).singleResult(); 			//获取历史流程实例
        List<HistoricActivityInstance> hais = historyService.createHistoricActivityInstanceQuery().processInstanceId(PROC_INST_ID_)
                .orderByHistoricActivityInstanceId().asc().list();	//获取流程中已经执行的节点，按照执行先后顺序排序
        BpmnModel bpmnModel = repositoryService.getBpmnModel(hip.getProcessDefinitionId()); // 获取bpmnModel
        List<FlowNode> historicFlowNodeList = new LinkedList<FlowNode>();					//全部活动实例
        for(HistoricActivityInstance hai : hais) {
            historicFlowNodeList.add((FlowNode) bpmnModel.getMainProcess().getFlowElement(hai.getActivityId(), true));
            if(hai.getAssignee() != null) {
                return hai.getAssignee();	//不为空的第一个节点办理人就是发起人
            }
        }
        return null;
    }*/



}
