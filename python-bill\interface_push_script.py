#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
接口推送脚本
用于推送电表信息和报账信息到指定的接口
"""

import requests
import time
import logging
from typing import List
import urllib3
import json
from datetime import datetime

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('interface_push.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class InterfacePusher:
    def __init__(self):
        self.base_url = "https://scnh.paas.sc.ctc.com/energy-cost/mssaccount/mssInterface"
        self.session = requests.Session()
        # 禁用SSL证书验证
        self.session.verify = False
        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'Python-Interface-Pusher/1.0'
        })

        # 创建响应日志文件
        self.response_log_file = f"interface_responses_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

        # 报账单ID列表
        self.bill_ids = [
"4707935892369711104",
"4707567522457722880",
"4708023626069319680",
"4709088917750677504",
"4709382278139224064",
"4709858695188717568",
"4710131174641651712",
"4709382278139224064"
        ]

    def save_response_log(self, interface_type: str, bill_id: str, url: str, status_code: int, headers: dict, response_text: str, attempt: int = 1):
        """
        保存响应日志到文件

        Args:
            interface_type: 接口类型（电表信息/报账信息）
            bill_id: 报账单ID
            url: 请求URL
            status_code: 响应状态码
            headers: 响应头
            response_text: 响应内容
            attempt: 尝试次数
        """
        log_entry = {
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "interface_type": interface_type,
            "bill_id": bill_id,
            "url": url,
            "attempt": attempt,
            "status_code": status_code,
            "headers": headers,
            "response_text": response_text,
            "success": status_code == 200
        }

        try:
            with open(self.response_log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False, indent=2) + "\n" + "="*80 + "\n")
        except Exception as e:
            logger.error(f"保存响应日志失败: {e}")
    
    def sync_energy_meter_info(self, bill_id: str, max_retries: int = 3) -> bool:
        """
        推送电表信息（只推送一次）

        Args:
            bill_id: 报账单ID
            max_retries: 最大重试次数

        Returns:
            bool: 推送是否成功
        """
        url = f"{self.base_url}/syncEnergyMeterInfosBybill/{bill_id}"

        for attempt in range(max_retries):
            try:
                logger.info(f"开始推送电表信息，报账单ID: {bill_id} (尝试 {attempt + 1}/{max_retries})")
                response = self.session.get(url, timeout=30, verify=False)

                # 记录详细的响应信息
                logger.info(f"接口响应 - 报账单ID: {bill_id}, 状态码: {response.status_code}")
                logger.info(f"响应头: {dict(response.headers)}")
                logger.info(f"响应内容: {response.text}")

                # 保存响应日志到文件
                self.save_response_log(
                    interface_type="电表信息推送",
                    bill_id=bill_id,
                    url=url,
                    status_code=response.status_code,
                    headers=dict(response.headers),
                    response_text=response.text,
                    attempt=attempt + 1
                )

                if response.status_code == 200:
                    logger.info(f"电表信息推送成功，报账单ID: {bill_id}")
                    return True
                else:
                    logger.error(f"电表信息推送失败，报账单ID: {bill_id}, 状态码: {response.status_code}")
                    if attempt < max_retries - 1:
                        logger.info(f"等待2秒后重试...")
                        time.sleep(2)

            except requests.exceptions.SSLError as e:
                logger.error(f"SSL错误，报账单ID: {bill_id}, 错误: {str(e)}")
                if attempt < max_retries - 1:
                    logger.info(f"等待2秒后重试...")
                    time.sleep(2)
            except requests.exceptions.RequestException as e:
                logger.error(f"电表信息推送异常，报账单ID: {bill_id}, 错误: {str(e)}")
                if attempt < max_retries - 1:
                    logger.info(f"等待2秒后重试...")
                    time.sleep(2)

        return False
    
    def sync_writeoff_info(self, bill_id: str, max_retries: int = 3) -> bool:
        """
        推送报账信息

        Args:
            bill_id: 报账单ID
            max_retries: 最大重试次数

        Returns:
            bool: 推送是否成功
        """
        url = f"{self.base_url}/syncWriteoffInfos/{bill_id}"

        for attempt in range(max_retries):
            try:
                logger.info(f"开始推送报账信息，报账单ID: {bill_id} (尝试 {attempt + 1}/{max_retries})")
                response = self.session.get(url, timeout=30, verify=False)

                # 记录详细的响应信息
                logger.info(f"接口响应 - 报账单ID: {bill_id}, 状态码: {response.status_code}")
                logger.info(f"响应头: {dict(response.headers)}")
                logger.info(f"响应内容: {response.text}")

                # 保存响应日志到文件
                self.save_response_log(
                    interface_type="报账信息推送",
                    bill_id=bill_id,
                    url=url,
                    status_code=response.status_code,
                    headers=dict(response.headers),
                    response_text=response.text,
                    attempt=attempt + 1
                )

                if response.status_code == 200:
                    logger.info(f"报账信息推送成功，报账单ID: {bill_id}")
                    return True
                else:
                    logger.error(f"报账信息推送失败，报账单ID: {bill_id}, 状态码: {response.status_code}")
                    if attempt < max_retries - 1:
                        logger.info(f"等待2秒后重试...")
                        time.sleep(2)

            except requests.exceptions.SSLError as e:
                logger.error(f"SSL错误，报账单ID: {bill_id}, 错误: {str(e)}")
                if attempt < max_retries - 1:
                    logger.info(f"等待2秒后重试...")
                    time.sleep(2)
            except requests.exceptions.RequestException as e:
                logger.error(f"报账信息推送异常，报账单ID: {bill_id}, 错误: {str(e)}")
                if attempt < max_retries - 1:
                    logger.info(f"等待2秒后重试...")
                    time.sleep(2)

        return False
    
    def push_all_energy_meters(self, delay: float = 1.0) -> dict:
        """
        推送所有电表信息（只推送一次）
        
        Args:
            delay: 每次请求之间的延迟时间（秒）
            
        Returns:
            dict: 推送结果统计
        """
        logger.info("开始批量推送电表信息")
        
        success_count = 0
        failed_count = 0
        failed_ids = []
        
        # 去重处理
        unique_bill_ids = list(set(self.bill_ids))
        logger.info(f"去重后共有 {len(unique_bill_ids)} 个报账单ID需要推送电表信息")
        
        for i, bill_id in enumerate(unique_bill_ids, 1):
            logger.info(f"进度: {i}/{len(unique_bill_ids)}")
            
            if self.sync_energy_meter_info(bill_id):
                success_count += 1
            else:
                failed_count += 1
                failed_ids.append(bill_id)
            
            # 添加延迟，避免请求过于频繁
            if i < len(unique_bill_ids):
                time.sleep(delay)
        
        result = {
            'total': len(unique_bill_ids),
            'success': success_count,
            'failed': failed_count,
            'failed_ids': failed_ids
        }
        
        logger.info(f"电表信息推送完成，总计: {result['total']}, 成功: {result['success']}, 失败: {result['failed']}")
        return result
    
    def push_all_writeoff_infos(self, delay: float = 1.0) -> dict:
        """
        推送所有报账信息
        
        Args:
            delay: 每次请求之间的延迟时间（秒）
            
        Returns:
            dict: 推送结果统计
        """
        logger.info("开始批量推送报账信息")
        
        success_count = 0
        failed_count = 0
        failed_ids = []
        
        for i, bill_id in enumerate(self.bill_ids, 1):
            logger.info(f"进度: {i}/{len(self.bill_ids)}")
            
            if self.sync_writeoff_info(bill_id):
                success_count += 1
            else:
                failed_count += 1
                failed_ids.append(bill_id)
            
            # 添加延迟，避免请求过于频繁
            if i < len(self.bill_ids):
                time.sleep(delay)
        
        result = {
            'total': len(self.bill_ids),
            'success': success_count,
            'failed': failed_count,
            'failed_ids': failed_ids
        }
        
        logger.info(f"报账信息推送完成，总计: {result['total']}, 成功: {result['success']}, 失败: {result['failed']}")
        return result


def main():
    """主函数"""
    pusher = InterfacePusher()
    
    print("=" * 50)
    print("接口推送脚本")
    print("=" * 50)
    print(f"响应日志将保存到: {pusher.response_log_file}")
    print(f"运行日志将保存到: interface_push.log")
    
    while True:
        print("\n请选择操作:")
        print("1. 推送所有电表信息（只推送一次）")
        print("2. 推送所有报账信息")
        print("3. 推送单个电表信息")
        print("4. 推送单个报账信息")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            delay = float(input("请输入请求间隔时间（秒，默认1.0）: ") or "1.0")
            result = pusher.push_all_energy_meters(delay)
            print(f"\n推送结果: {result}")
            
        elif choice == "2":
            delay = float(input("请输入请求间隔时间（秒，默认1.0）: ") or "1.0")
            result = pusher.push_all_writeoff_infos(delay)
            print(f"\n推送结果: {result}")
            
        elif choice == "3":
            bill_id = input("请输入报账单ID: ").strip()
            if bill_id:
                success = pusher.sync_energy_meter_info(bill_id)
                print(f"推送结果: {'成功' if success else '失败'}")
            else:
                print("报账单ID不能为空")
                
        elif choice == "4":
            bill_id = input("请输入报账单ID: ").strip()
            if bill_id:
                success = pusher.sync_writeoff_info(bill_id)
                print(f"推送结果: {'成功' if success else '失败'}")
            else:
                print("报账单ID不能为空")
                
        elif choice == "5":
            print("退出程序")
            break
            
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
