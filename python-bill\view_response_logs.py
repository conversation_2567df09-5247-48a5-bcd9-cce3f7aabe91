#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
响应日志查看器
用于查看和分析接口响应日志
"""

import json
import glob
import os
from datetime import datetime

def view_response_logs():
    """查看响应日志"""
    # 查找所有响应日志文件
    log_files = glob.glob("interface_responses_*.log")
    
    if not log_files:
        print("未找到响应日志文件")
        return
    
    # 按修改时间排序，最新的在前
    log_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    
    print("找到以下响应日志文件:")
    for i, file in enumerate(log_files, 1):
        mtime = datetime.fromtimestamp(os.path.getmtime(file))
        print(f"{i}. {file} (修改时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')})")
    
    # 选择文件
    try:
        choice = int(input(f"\n请选择要查看的文件 (1-{len(log_files)}): "))
        if choice < 1 or choice > len(log_files):
            print("无效选择")
            return
        
        selected_file = log_files[choice - 1]
        
    except ValueError:
        print("无效输入")
        return
    
    # 读取并分析日志
    print(f"\n正在分析文件: {selected_file}")
    print("=" * 80)
    
    success_count = 0
    failed_count = 0
    total_count = 0
    
    try:
        with open(selected_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 按分隔符分割日志条目
        entries = content.split("=" * 80)
        
        for entry in entries:
            entry = entry.strip()
            if not entry:
                continue
                
            try:
                log_data = json.loads(entry)
                total_count += 1
                
                print(f"\n--- 记录 {total_count} ---")
                print(f"时间: {log_data.get('timestamp', 'N/A')}")
                print(f"接口类型: {log_data.get('interface_type', 'N/A')}")
                print(f"报账单ID: {log_data.get('bill_id', 'N/A')}")
                print(f"尝试次数: {log_data.get('attempt', 'N/A')}")
                print(f"状态码: {log_data.get('status_code', 'N/A')}")
                print(f"成功: {'✅' if log_data.get('success', False) else '❌'}")
                print(f"响应内容: {log_data.get('response_text', 'N/A')}")
                
                if log_data.get('success', False):
                    success_count += 1
                else:
                    failed_count += 1
                    
            except json.JSONDecodeError:
                continue
    
    except Exception as e:
        print(f"读取日志文件失败: {e}")
        return
    
    # 显示统计信息
    print("\n" + "=" * 80)
    print("统计信息:")
    print(f"总记录数: {total_count}")
    print(f"成功: {success_count}")
    print(f"失败: {failed_count}")
    if total_count > 0:
        success_rate = (success_count / total_count) * 100
        print(f"成功率: {success_rate:.2f}%")

def view_summary():
    """查看所有日志文件的汇总信息"""
    log_files = glob.glob("interface_responses_*.log")
    
    if not log_files:
        print("未找到响应日志文件")
        return
    
    print("所有响应日志汇总:")
    print("=" * 80)
    
    total_success = 0
    total_failed = 0
    total_records = 0
    
    for log_file in log_files:
        print(f"\n文件: {log_file}")
        
        success_count = 0
        failed_count = 0
        record_count = 0
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            entries = content.split("=" * 80)
            
            for entry in entries:
                entry = entry.strip()
                if not entry:
                    continue
                    
                try:
                    log_data = json.loads(entry)
                    record_count += 1
                    
                    if log_data.get('success', False):
                        success_count += 1
                    else:
                        failed_count += 1
                        
                except json.JSONDecodeError:
                    continue
        
        except Exception as e:
            print(f"  读取失败: {e}")
            continue
        
        print(f"  记录数: {record_count}, 成功: {success_count}, 失败: {failed_count}")
        if record_count > 0:
            success_rate = (success_count / record_count) * 100
            print(f"  成功率: {success_rate:.2f}%")
        
        total_records += record_count
        total_success += success_count
        total_failed += failed_count
    
    print("\n" + "=" * 80)
    print("总体统计:")
    print(f"总记录数: {total_records}")
    print(f"总成功: {total_success}")
    print(f"总失败: {total_failed}")
    if total_records > 0:
        overall_success_rate = (total_success / total_records) * 100
        print(f"总体成功率: {overall_success_rate:.2f}%")

def main():
    """主函数"""
    print("响应日志查看器")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 查看详细日志")
        print("2. 查看汇总信息")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            view_response_logs()
        elif choice == "2":
            view_summary()
        elif choice == "3":
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
