package com.sccl.modules.mssaccount.mssinterface.controller;

import com.sccl.common.web.controller.BaseController;
import com.sccl.framework.web.domain.AjaxResult;
import com.sccl.modules.mssaccount.mssinterface.service.CollectMeterSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * 报账明细电量推送控制器
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@RestController
@Slf4j
@RequestMapping("/mssaccount/collectMeterSync")
public class CollectMeterSyncController extends BaseController {

    @Autowired
    private CollectMeterSyncService collectMeterSyncService;

    /**
     * 推送全部报账明细电量
     * 根据期号查询对应的collectmeter_v2_期号表，推送全部数据
     *
     * @param periodNumber 期号，例如：202507
     * @return 推送结果
     */
    @PostMapping("/syncAllByPeriod")
    @ResponseBody
    public AjaxResult syncAllCollectMeterByPeriod(@RequestParam("periodNumber") String periodNumber) {
        try {
            // 参数验证
            if (periodNumber == null || periodNumber.trim().isEmpty()) {
                return AjaxResult.error("期号不能为空");
            }

            // 验证期号格式（6位数字，如202507）
            if (!periodNumber.matches("\\d{6}")) {
                return AjaxResult.error("期号格式不正确，应为6位数字，如：202507");
            }

            log.info("开始推送全部报账明细电量，期号：{}", periodNumber);
            String result = collectMeterSyncService.syncAllCollectMeterByPeriod(periodNumber);
            log.info("推送全部报账明细电量完成，期号：{}，结果：{}", periodNumber, result);
            return AjaxResult.success(result);

        } catch (Exception e) {
            log.error("推送全部报账明细电量异常，期号：{}", periodNumber, e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return AjaxResult.error("推送失败：" + e.getMessage());
        }
    }

    /**
     * 查询指定期号表的数据统计信息
     *
     * @param periodNumber 期号，例如：202507
     * @return 统计信息
     */
    @GetMapping("/getTableStats")
    @ResponseBody
    public AjaxResult getTableStats(@RequestParam("periodNumber") String periodNumber) {
        try {
            // 参数验证
            if (periodNumber == null || periodNumber.trim().isEmpty()) {
                return AjaxResult.error("期号不能为空");
            }

            // 验证期号格式（6位数字，如202507）
            if (!periodNumber.matches("\\d{6}")) {
                return AjaxResult.error("期号格式不正确，应为6位数字，如：202507");
            }

            log.info("查询期号表统计信息，期号：{}", periodNumber);
            String result = collectMeterSyncService.getTableStats(periodNumber);
            log.info("查询期号表统计信息完成，期号：{}，结果：{}", periodNumber, result);
            return AjaxResult.success(result);

        } catch (Exception e) {
            log.error("查询期号表统计信息异常，期号：{}", periodNumber, e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 分页推送报账明细电量
     * 支持大数据量分批推送
     *
     * @param periodNumber 期号，例如：202507
     * @param pageSize 每页大小，默认1000
     * @param maxPages 最大页数，默认不限制
     * @return 推送结果
     */
    @PostMapping("/syncByPage")
    @ResponseBody
    public AjaxResult syncCollectMeterByPage(
            @RequestParam("periodNumber") String periodNumber,
            @RequestParam(value = "pageSize", defaultValue = "1000") Integer pageSize,
            @RequestParam(value = "maxPages", required = false) Integer maxPages) {
        try {
            // 参数验证
            if (periodNumber == null || periodNumber.trim().isEmpty()) {
                return AjaxResult.error("期号不能为空");
            }

            // 验证期号格式（6位数字，如202507）
            if (!periodNumber.matches("\\d{6}")) {
                return AjaxResult.error("期号格式不正确，应为6位数字，如：202507");
            }

            if (pageSize <= 0 || pageSize > 5000) {
                return AjaxResult.error("每页大小必须在1-5000之间");
            }

            if (maxPages != null && maxPages <= 0) {
                return AjaxResult.error("最大页数必须大于0");
            }

            log.info("开始分页推送报账明细电量，期号：{}，每页大小：{}，最大页数：{}", periodNumber, pageSize, maxPages);
            String result = collectMeterSyncService.syncCollectMeterByPage(periodNumber, pageSize, maxPages);
            log.info("分页推送报账明细电量完成，期号：{}，结果：{}", periodNumber, result);
            return AjaxResult.success(result);

        } catch (Exception e) {
            log.error("分页推送报账明细电量异常，期号：{}", periodNumber, e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return AjaxResult.error("推送失败：" + e.getMessage());
        }
    }
}
