<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sccl.modules.business.syncresult.mapper.SyncresultMapper">

    <resultMap type="Syncresult" id="SyncresultResult">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="method" column="method"/>
        <result property="result" column="result"/>
        <result property="operName" column="oper_name"/>
        <result property="delFlag" column="del_flag"/>
        <result property="msg" column="msg"/>
        <result property="num" column="num"/>
        <result property="operTime" column="oper_time"/>
    </resultMap>

    <sql id="selectVo">
        select id,
               title,
               method,
               result,
               oper_name,
               del_flag,
               msg,
               num,
               oper_time
        from syncresult
    </sql>

    <sql id="other-condition">
        <if test="id != null">and id = #{id}</if>
        <if test="title != null">and title = #{title}</if>
        <if test="method != null">and method = #{method}</if>
        <if test="result != null">and result = #{result}</if>
        <if test="operName != null">and oper_name = #{operName}</if>
        <if test="delFlag != null">and del_flag = #{delFlag}</if>
        <if test="msg != null">and msg = #{msg}</if>
        <if test="num != null">and num = #{num}</if>
        <if test="operTime != null">and oper_time = #{operTime}</if>
    </sql>
    <sql id="failCollectMeter-condition">
        <if test="collectTime != null">and collectTime = #{collectTime}</if>
        <if test="failMag != null">and failMag = #{failMag}</if>
    </sql>

    <sql id="like-condition">
        <if test="id != null">and id like concat('%', #{id}, '%')</if>
        <if test="title != null">and title like concat('%', #{title}, '%')</if>
        <if test="method != null">and method like concat('%', #{method}, '%')</if>
        <if test="result != null">and result like concat('%', #{result}, '%')</if>
        <if test="operName != null">and oper_name like concat('%', #{operName}, '%')</if>
        <if test="delFlag != null">and del_flag like concat('%', #{delFlag}, '%')</if>
        <if test="msg != null">and msg like concat('%', #{msg}, '%')</if>
        <if test="num != null">and num like concat('%', #{num}, '%')</if>
        <if test="operTime != null">and oper_time like concat('%', #{operTime}, '%')</if>
    </sql>

    <select id="selectList" parameterType="Syncresult" resultMap="SyncresultResult">
        <include refid="selectVo"/>
        <where>
            del_flag = '0'
            <include refid="other-condition"/>
        </where>
    </select>

    <select id="selectByLike" parameterType="Syncresult" resultMap="SyncresultResult">
        <include refid="selectVo"/>
        <where>
            del_flag = '0'
            <include refid="like-condition"/>
        </where>
    </select>

    <select id="selectByMap" resultMap="SyncresultResult">
        <include refid="selectVo"/>
        <where>
            del_flag = '0'
            <if test="findBy != null">
                <include refid="other-condition"/>
            </if>
            <if test="findLikeBy != null">
                <include refid="like-condition"/>
            </if>
        </where>
    </select>

    <select id="selectByPrimaryKey" parameterType="Map" resultMap="SyncresultResult">
        <include refid="selectVo"/>
        where del_flag = '0' and id = #{id}
        <if test="shardKey != null and shardKey != ''">and shardKey = #{shardKey}</if>
    </select>

    <select id="count" parameterType="Syncresult" resultType="Integer">
        select count(*) from syncresult
        <where>
            del_flag = '0'
            <include refid="other-condition"/>
        </where>
    </select>
    <select id="selectAllstation" resultType="java.lang.String">
        select stationCode
        from meterinfo
        where syncFlag != 2
        <if test="stationcode!='all'">
            and stationCode = #{stationcode}
        </if>
        and del_flag=0
        group by stationCode
    </select>
    <!--    <select id="selectcolleterPerfects" resultType="com.sccl.modules.dataperfect.domain.ColleterPerfect">
            select case when left(p.electrotype, 2) != '14' then si.resstationcode else si.id end stationcode,
                   left(p.electrotype, 2)                                                         type,
                   avg(pa.totalusedreadings / (datediff(pa.enddate, pa.startdate) + 1))           avgpower
            from power_ammeterorprotocol p,
                 power_account pa,
                 mss_r_billitem_account r,
                 mss_accountbill ma,
                 power_station_info si
            where p.id = pa.ammeterid
              and r.account_id = pa.pcid
              and ma.ID = r.bill_id
              and p.stationcode = si.id
              and ma.BUDGETSETNAME = #{lastMonth}
            group by p.stationaddresscode
        </select>-->


    <resultMap id="meterInfoTo3" type="com.sccl.modules.mssaccount.mssinterface.domain.MeterInfo3">
        <result property="usage" column="usageCopy"/>
    </resultMap>

    <select id="selectJzForMeterinfo" resultMap="meterInfoTo3">
        SELECT '45' energyType,
        '1' typeStationCode,
        (case when pap.price is null then 0.65 else pap.price end) contractPrice,
        '26' provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
        (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company) cityName,
        pap.country countyCode,
        (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        ifnull(pap.supplybureauammetercode, '') powerGridEnergyMeterCode,
        ifnull(pap.projectname, '未知') energyMeterName,
        (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END) `status`,
        (select type_code from power_category_type where type_category = 'ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END) type,
        (case when sj.jtlte_code is not null then sj.jtlte_code else '未知' end) stationCode,
        (case when sj.jtlte_name is not null then sj.jtlte_name else '未知' end) stationName,
        ifnull(IFNULL(si.address,
        si.stationname), '未知') stationLocation,
        (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END) stationStatus,
        rpad(pap.electrotype, 4, 0) stationType,
        IFNULL(si.isbigfactories, 0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag, 1) energySupplyWay,
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) siteCode
        FROM (select *
        from power_ammeterorprotocol
        where del_flag = '0'
        and status = 1
        and electrotype in
        (1411, 1412, 1421, 1422, 1431, 1432)
        ) pap
        inner join power_station_info si on si.id = pap.stationcode
        inner join (select jjt.*
        from power_station_info_r_jtlte jjt
        INNER JOIN (select max(id) mid, stationid
        from power_station_info_r_jtlte
        group by stationid) f on jjt.stationid = f.stationid and jjt.id = f.mid) sj on
        sj.stationid = si.id and sj.jtlte_code in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectJznotForMeterinfo" resultMap="meterInfoTo3">
        SELECT '45' energyType,
        '1' typeStationCode,
        (case when pap.price is null then 0.65 else pap.price end) contractPrice,
        '26' provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
        (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company) cityName,
        pap.country countyCode,
        (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        ifnull(pap.supplybureauammetercode, '') powerGridEnergyMeterCode,
        ifnull(pap.projectname, '未知') energyMeterName,
        (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END) `status`,
        (select type_code from power_category_type where type_category = 'ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END) type,
        (case
        when si.resstationcode is not null then si.resstationcode
        else
        '未知' end) stationCode,
        (case when si.resstationname is not null then si.resstationname else '未知' end) stationName,
        ifnull(IFNULL(si.address,
        si.stationname), '未知') stationLocation,
        (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END) stationStatus,
        rpad(pap.electrotype, 4, 0) stationType,
        IFNULL(si.isbigfactories, 0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag, 1) energySupplyWay,
        (case
        when si.resstationcode is not null then si.resstationcode
        else
        '' end) siteCode
        FROM (select *
        from power_ammeterorprotocol
        where del_flag = '0'
        and status = 1
        and electrotype not in
        (1411, 1412, 1421, 1422, 1431, 1432)
        and stationaddresscode in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ) pap inner join power_station_info si on si.id = pap.stationcode
    </select>
    <select id="selectForMeterinfoAll" resultMap="meterInfoTo3">
        <if test="list !=null and list.size()>0">
            SELECT '45' energyType,
            '1' typeStationCode,
            (case when pap.price is null then 0.65 else pap.price end) contractPrice,
            '26' provinceCode,
            (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
            (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company) cityName,
            pap.country countyCode,
            (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
            (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
            ifnull(pap.supplybureauammetercode, '') powerGridEnergyMeterCode,
            ifnull(pap.projectname, '未知') energyMeterName,
            (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END) `status`,
            (select type_code from power_category_type where type_category = 'ammeterUse' and type_code =
            pap.ammeteruse)
            `usage`,
            (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END) type,
            (case when sj.jtlte_code is not null then sj.jtlte_code else '未知' end) stationCode,
            (case when sj.jtlte_name is not null then sj.jtlte_name else '未知' end) stationName,
            ifnull(IFNULL(si.address,
            si.stationname), '未知') stationLocation,
            (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END) stationStatus,
            rpad(pap.electrotype, 4, 0) stationType,
            IFNULL(si.isbigfactories, 0) largeIndustrialElectricityFlag,
            IFNULL(pap.directsupplyflag, 1) energySupplyWay,
            (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) siteCode
            FROM (select *
            from power_ammeterorprotocol
            where del_flag = '0'
            and status = 1
            and electrotype in
            (1411, 1412, 1421, 1422, 1431, 1432)
            ) pap
            inner join power_station_info si on si.id = pap.stationcode
            inner join (select jjt.*
            from power_station_info_r_jtlte jjt
            INNER JOIN (select max(id) mid, stationid
            from power_station_info_r_jtlte
            group by stationid) f on jjt.stationid = f.stationid and jjt.id = f.mid) sj on
            sj.stationid = si.id and sj.jtlte_code in
            <foreach collection="list" item="item" close=")" open="(" separator=",">
                #{item}
            </foreach>
            union all
            SELECT '45' energyType,
            '1' typeStationCode,
            (case when pap.price is null then 0.65 else pap.price end) contractPrice,
            '26' provinceCode,
            (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
            (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company) cityName,
            pap.country countyCode,
            (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
            (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
            ifnull(pap.supplybureauammetercode, '') powerGridEnergyMeterCode,
            ifnull(pap.projectname, '未知') energyMeterName,
            (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END) `status`,
            (select type_code from power_category_type where type_category = 'ammeterUse' and type_code =
            pap.ammeteruse)
            `usage`,
            (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END) type,
            (case
            when si.resstationcode is not null then si.resstationcode
            else
            '未知' end) stationCode,
            (case when si.resstationname is not null then si.resstationname else '未知' end) stationName,
            ifnull(IFNULL(si.address,
            si.stationname), '未知') stationLocation,
            (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END) stationStatus,
            rpad(pap.electrotype, 4, 0) stationType,
            IFNULL(si.isbigfactories, 0) largeIndustrialElectricityFlag,
            IFNULL(pap.directsupplyflag, 1) energySupplyWay,
            (case
            when si.resstationcode is not null then si.resstationcode
            else
            '' end) siteCode
            FROM (select *
            from power_ammeterorprotocol
            where del_flag = '0'
            and status = 1
            and electrotype not in
            (1411, 1412, 1421, 1422, 1431, 1432)
            and stationaddresscode in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            ) pap inner join power_station_info si on si.id = pap.stationcode
        </if>
        <if test="list == null or list.size() == 0">
            SELECT * FROM meterinfo WHERE 1=0
        </if>
    </select>
    <select id="selectMeterinfo" resultMap="meterInfoTo3">
        SELECT '45'                                                                               energyType,
               '1'                                                                                typeStationCode,
               (case when pap.price is null then 0.65 else pap.price end)                         contractPrice,
               '26'                                                                               provinceCode,
               (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
               (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company)           cityName,
               pap.country                                                                        countyCode,
               (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country)                countyName,
               (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END)        energyMeterCode,
               ifnull(pap.supplybureauammetercode, '')                                            powerGridEnergyMeterCode,
               ifnull(pap.projectname, '未知')                                                      energyMeterName,
               (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END)                         `status`,
               (select type_code
                from power_category_type
                where type_category = 'ammeterUse'
                  and type_code = pap.ammeteruse)
                                                                                                  `usage`,
               (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END)                                type,
               (case when sj.jtlte_code is not null then sj.jtlte_code else '未知' end)             stationCode,
               (case when sj.jtlte_name is not null then sj.jtlte_name else '未知' end)             stationName,
               ifnull(IFNULL(si.address,
                             si.stationname), '未知')                                               stationLocation,
               (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END)                                        stationStatus,
               rpad(pap.electrotype, 4, 0)                                                        stationType,
               IFNULL(si.isbigfactories, 0)                                                       largeIndustrialElectricityFlag,
               IFNULL(pap.directsupplyflag, 1)                                                    energySupplyWay,
               (case when sj.jtlte_code is not null then sj.jtlte_code else '' end)               siteCode
        FROM (select *
              from power_ammeterorprotocol
              where del_flag = '0'
                and status = 1
                and electrotype in
                    (1411, 1412, 1421, 1422, 1431, 1432)
             ) pap
                 inner join power_station_info si on si.id = pap.stationcode
                 inner join (select jjt.*
                             from power_station_info_r_jtlte jjt
                                      INNER JOIN (select max(id) mid, stationid
                                                  from power_station_info_r_jtlte
                                                  group by stationid) f
                                                 on jjt.stationid = f.stationid and jjt.id = f.mid) sj on
            sj.stationid = si.id and sj.jtlte_code = #{stationcode}
        union all
        SELECT '45'                                                                               energyType,
               '1'                                                                                typeStationCode,
               (case when pap.price is null then 0.65 else pap.price end)                         contractPrice,
               '26'                                                                               provinceCode,
               (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
               (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company)           cityName,
               pap.country                                                                        countyCode,
               (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country)                countyName,
               (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END)        energyMeterCode,
               ifnull(pap.supplybureauammetercode, '')                                            powerGridEnergyMeterCode,
               ifnull(pap.projectname, '未知')                                                      energyMeterName,
               (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END)                         `status`,
               (select type_code
                from power_category_type
                where type_category = 'ammeterUse'
                  and type_code = pap.ammeteruse)
                                                                                                  `usage`,
               (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END)                                type,
               (case
                    when si.resstationcode is not null then si.resstationcode
                    else
                        '未知' end)                                                                 stationCode,
               (case when si.resstationname is not null then si.resstationname else '未知' end)     stationName,
               ifnull(IFNULL(si.address,
                             si.stationname), '未知')                                               stationLocation,
               (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END)                                        stationStatus,
               rpad(pap.electrotype, 4, 0)                                                        stationType,
               IFNULL(si.isbigfactories, 0)                                                       largeIndustrialElectricityFlag,
               IFNULL(pap.directsupplyflag, 1)                                                    energySupplyWay,
               (case
                    when si.resstationcode is not null then si.resstationcode
                    else
                        '' end)                                                                   siteCode
        FROM (select *
              from power_ammeterorprotocol
              where del_flag = '0'
                and status = 1
                and electrotype not in
                    (1411, 1412, 1421, 1422, 1431, 1432)
                and stationaddresscode = #{stationcode}
             ) pap
                 inner join power_station_info si on si.id = pap.stationcode
    </select>
    <select id="selectDelete" resultMap="meterInfoTo3">
        <trim suffixOverrides="union all">
            <foreach collection="list" item="item">
                select *
                from meterinfo where syncFlag!=2 and stationCode=#{item.stationCode} and
                energyMeterCode!=#{item.energyMeterCode} and del_flag=0
                union all
            </foreach>
        </trim>
    </select>
    <select id="selectNewStationcdoes" resultType="java.lang.String">
        SELECT stationCode
        FROM (
        <trim suffixOverrides="UNION ALL">
            <foreach collection="list" item="item">
                SELECT #{item} AS stationCode UNION ALL
            </foreach>
        </trim>
        ) AS input_strings
        WHERE NOT EXISTS (
        SELECT 1
        FROM meterinfo
        WHERE stationCode IN (input_strings.stationCode) and syncFlag!=2
        )
    </select>
    <select id="selectJzStationcode" resultType="java.lang.String">
        select concat(jjt.stationid,'->',jjt.jtlte_code)
        from power_station_info_r_jtlte jjt
        INNER JOIN (select max(id) mid, stationid
        from power_station_info_r_jtlte where stationid in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by stationid) f on jjt.stationid = f.stationid and jjt.id = f.mid
    </select>
    <select id="selectSysnc" resultMap="meterInfoTo3">
        select *
        from meterinfo where del_flag=0 and syncFlag=3 and stationCode in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectContractPrice" resultType="java.lang.String">
        select concat(case when pap.category=1 then pap.ammetername else pap.protocolname end ,
        '/',
        ifnull(pap.price,'0.65')
        )
        from power_ammeterorprotocol pap
        where case when pap.category=1 then pap.ammetername else pap.protocolname end
        in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and pap.status=1
    </select>
    <select id="getauditFlag" resultType="java.lang.Boolean">
        select del_flag
        from auditresultvo
        where id = -1
    </select>
    <select id="selectFailCollectMeter" resultType="com.sccl.modules.dataperfect.domain.CollectMeterVo">
        select collectTime,
               failMag,
               case when syncFlag = 1 then '推送成功' when syncFlag = 2 then '推送失败' end synfFlag,
               count(*)                                                             num
        from fail_sync_collectmeter
        where del_flag = 0
        group by collectTime, substring_index(failMag, '||', '-1'), syncFlag
        limit #{limit} offset #{offset}
    </select>
    <select id="selectFailCollectMeterByVo"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeterFail">
        select *
        from fail_sync_collectmeter
        where del_flag=0 and syncFlag=2
        <include refid="failCollectMeter-condition"/>
    </select>
    <select id="selectExists" resultType="com.sccl.modules.mssaccount.mssinterface.domain.MeterInfo2">
        SELECT *
        FROM meterinfo
        WHERE (provinceCode, cityCode, countyCode, energyMeterCode) IN
        <foreach collection="list" item="param" separator="," open="(" close=")">
            (#{param.provinceCode}, #{param.cityCode}, #{param.countyCode}, #{param.energyMeterCode})
        </foreach>
        and del_flag=0
    </select>
    <select id="selectResstationcode" resultType="java.lang.String">
        select t.resstationcode
        from temp_t t
        limit #{limit} offset #{offset};
    </select>
    <select id="selectTempt" resultType="com.sccl.modules.dataperfect.domain.Tempt">
        select t.resstationcode,
               t.startdate,
               t.enddate,
               ifnull(round(t.avg, 2), 0) avgForBill
        from temp_t t
        limit #{limit} offset #{offset};
    </select>
    <select id="collectAll" resultType="com.sccl.modules.dataperfect.domain.AccountTime">
        select distinct
               r.id rid,p.stationaddresscode,
               pa.startdate,
               pa.enddate
        from power_ammeterorprotocol p,
             power_account pa,
             mss_r_billitem_account r,
             mss_accountbill ma,
             mss_accountbillitem mi,
             power_station_info si
        where p.id = pa.ammeterid
          and r.account_id = pa.pcid
          and ma.ID = r.bill_id
          and ma.ID = mi.WRITEOFF_INSTANCE_ID
          and p.stationcode = si.id
          and ma.YEAR=substring(#{budget},1,4)
          and ma.BIZ_ENTRY_CODE=substring(#{budget},6,7)
          and  r.money>0
union
        select distinct
                        r.id rid,
                        p.stationaddresscode,
                        pa.startdate,
                        pa.enddate
        from power_ammeterorprotocol_record p,
             power_account pa,
             mss_r_billitem_account r,
             mss_accountbill ma,
             mss_accountbillitem mi,
             power_station_info si
        where p.ammeter_protocol_id = pa.ammeterid
          and r.account_id = pa.pcid
          and ma.ID = r.bill_id
          and mi.BUDGET_TYPE = 1
          and ma.ID = mi.WRITEOFF_INSTANCE_ID
          and p.stationcode = si.id
          and ma.YEAR=substring(#{budget},1,4)
          and ma.BIZ_ENTRY_CODE=substring(#{budget},6,7)
          and  r.money>0
         </select>
    <select id="collectAllPro" resultType="com.sccl.modules.dataperfect.domain.AccountTime">
        select distinct si.resstationcode    stationaddresscode,
                        energyMeterCode,
                        electricityStartDate startdate,
                        electricityEndDate   enddate
        from meterdatesfortwoc m,
        (select a.*
        from (select stationcode, max(CREATE_TIME) CREATE_TIME
        from power_ammeterorprotocol_record
        where create_time &lt; DATE_ADD(concat(#{budget}, '-01'), INTERVAL 1 MONTH)
        group by stationcode) b,
        power_ammeterorprotocol_record a
        where a.stationcode = b.stationcode
        and a.CREATE_TIME = b.CREATE_TIME
                ) pa,
             power_station_info si
        where m.energyMeterCode = (case when pa.category = 1 then pa.ammetername else pa.protocolname end)
          and cast(pa.stationcode as signed ) = si.id
          and m.statisPeriod = replace(#{budget}, '-', '')
          and m.del_flag = 0
          and pa.status = 1
          and left (pa.electrotype
            , 2) in ('11'
            , '12'
            , '13', '14')
    </select>
    <select id="collectAllProGroupStationCode" resultType="com.sccl.modules.dataperfect.domain.AccountTime">
        select distinct si.resstationcode    stationaddresscode,
                        energyMeterCode,
                        electricityStartDate startdate,
                        electricityEndDate   enddate
        from meterdatesfortwoc m,
        (select a.*
        from (select stationcode, max(CREATE_TIME) CREATE_TIME
        from power_ammeterorprotocol_record
        where create_time &lt; DATE_ADD(concat(#{budget}, '-01'), INTERVAL 1 MONTH)
        group by stationcode) b,
        power_ammeterorprotocol_record a
        where a.stationcode = b.stationcode
        and a.CREATE_TIME = b.CREATE_TIME
                ) pa,
             power_station_info si
        where m.energyMeterCode = (case when pa.category = 1 then pa.ammetername else pa.protocolname end)
          and cast(pa.stationcode as signed ) = si.id
          and m.statisPeriod = replace(#{budget}, '-', '')
          and m.del_flag = 0
          and pa.status = 1
          and left (pa.electrotype
            , 2) in ('11'
            , '12'
            , '13', '14')
    </select>
    <select id="collectAllProGroupAmmeterpol" resultType="com.sccl.modules.dataperfect.domain.AccountTime">
        select distinct si.resstationcode    stationaddresscode,
                        energyMeterCode,
                        electricityStartDate startdate,
                        electricityEndDate   enddate
        from meterdatesfortwoc m,
        (select a.*
        from (select ammeter_protocol_id, max(CREATE_TIME) CREATE_TIME
        from power_ammeterorprotocol_record
        where create_time &lt; DATE_ADD(concat(#{budget}, '-01'), INTERVAL 1 MONTH)
        group by ammeter_protocol_id  ) b,
        power_ammeterorprotocol_record a
        where a.ammeter_protocol_id = b.ammeter_protocol_id
        and a.CREATE_TIME = b.CREATE_TIME
        ) pa,
             power_station_info si
        where m.energyMeterCode = (case when pa.category = 1 then pa.ammetername else pa.protocolname end)
          and cast(pa.stationcode as signed ) = si.id
          and m.statisPeriod = replace(#{budget}, '-', '')
          and m.del_flag = 0
          and pa.status = 1
          and left (pa.electrotype
            , 2) in ('11'
            , '12'
            , '13', '14')
    </select>
    <select id="collectAllProGroupAmmeterpoljtmss" resultType="com.sccl.modules.dataperfect.domain.AccountTime">
        SELECT DISTINCT
            si.resstationcode stationaddresscode,
            energyMeterCode,
            electricityStartDate startdate,
            electricityEndDate enddate
        FROM
            meterdatesfortwoc m,
            power_ammeterorprotocol_record pa,
            power_station_info si
        WHERE
            m.energyMeterCode = ( CASE WHEN pa.category = 1 THEN pa.ammetername ELSE pa.protocolname END )
          AND cast( pa.stationcode AS signed ) = si.id
          AND m.statisPeriod = REPLACE ( #{budget}, '-', '' )
          AND m.del_flag = 0
          AND si.resstationcode IN (
            SELECT
                stationcode
            FROM
                jt_mss_busi_bases jb
            WHERE
                jb.YEAR = SUBSTRING_INDEX( #{budget}, '-', 1 )
              AND jb.MONTH = SUBSTRING_INDEX( #{budget}, '-', - 1 )
        )
    </select>
    <select id="collectAllProGroupAmmeterpolConsist" resultType="com.sccl.modules.dataperfect.domain.AccountTime">
        SELECT
            stationCode stationaddresscode,
            startdate,
            enddate
        FROM
            (
                SELECT
                    vv.cityCode,
                    vv.cityName,
                    vv.countryName,
                    vv.countyCode,
                    vv.stationCode,
                    vv.stationName,
                    county_name,
                    min(start_date) startdate,
                    max(end_date)   enddate,
                    round(sum(mss_data) / count(DISTINCT (writeoff_instance_code)), 2)
                FROM
                    (
                        SELECT
                            mj.cityCode,
                            mj.cityName,
                            mj.countyCode,
                            mj.countyName countryName,
                            mj.stationCode,
                            mj.stationName,
                            round(sum(this_quantity_of_electricity) / sum(DATEDIFF(electricity_end_date, electricity_start_date) + 1), 2) AS mss_data,
                            writeoff_instance_code,
                            wdd.county_name,
                            wdd.energy_meter_code,
                            min(electricity_start_date)                                                                                      start_date,
                            max(electricity_end_date)                                                                                        end_date,
                            round(sum(this_quantity_of_electricity), 2),
                            sum(DATEDIFF(electricity_end_date, electricity_start_date) + 1)                                               AS days
                        FROM
                            (
                                SELECT
                                    gg.*
                                FROM (SELECT max(msg_id) mmsgid, billid FROM meter_info_db_bases wdd GROUP BY billid) dd
                                         INNER JOIN meter_info_db_bases gg ON gg.msg_id = dd.mmsgid
                                        AND dd.billid = gg.billid
                            ) mdb
                                INNER JOIN (
                                SELECT
                                    gg.*
                                FROM (SELECT max(msg_id) mmsgid, billid FROM writeoffinfodb wdd GROUP BY billid) dd
                                         INNER JOIN writeoffinfodb gg ON gg.msg_id = dd.mmsgid
                                        AND dd.billid = gg.billid
                            ) wdd ON mdb.billid = wdd.billid
                                AND mdb.energy_meter_code = wdd.energy_meter_code
                                LEFT JOIN meterinfo_all_jt mj ON mj.energyMeterCode = wdd.energy_meter_code and mj.status = '1'
                        WHERE
                            mdb.billid IN (
                                SELECT
                                    id
                                FROM
                                    mss_accountbill ma
                                WHERE ma.PICKING_MODE IN (1, 7, 9)
                                  AND ma.YEAR = SUBSTRING_INDEX(#{budget}, '-', 1)
                                  and ma.BIZ_ENTRY_CODE = SUBSTRING_INDEX(#{budget}, '-', -1))
                        group by COALESCE(mj.stationCode, mdb.energy_meter_code), COALESCE(writeoff_instance_code, mdb.billid)) vv
                group by COALESCE(vv.stationcode, vv.energy_meter_code) -- 确保即使没有stationcode也能分组
            ) bb
        WHERE stationCode is not null
        order by bb.cityCode,
                 bb.countyCode
    </select>
    <select id="collectAllProGroupAll" resultType="com.sccl.modules.dataperfect.domain.AccountTime">
        select distinct si.resstationcode    stationaddresscode,
        energyMeterCode,
        electricityStartDate startdate,
        electricityEndDate   enddate
        from meterdatesfortwoc m,
        (select (case when category = 1 then ammetername else protocolname end) energmetercode
        from power_ammeterorprotocol_record
        where left(electrotype, 2) in (11, 12, 13) and status=1
        group by ammeter_protocol_id) pa2,
        (select (case when a.category = 1 then a.ammetername else a.protocolname end) enermetercode,
        a.stationcode,
        a.status
        from (select stationcode, max(CREATE_TIME) CREATE_TIME
        from power_ammeterorprotocol_record
        where create_time &lt;DATE_ADD(concat(#{budget}, '-01'), INTERVAL 1 MONTH)
        group by stationcode) b,
        power_ammeterorprotocol_record a
        where a.stationcode = b.stationcode
        and a.CREATE_TIME = b.CREATE_TIME) pa1,
        power_station_info si
        where
            m.energyMeterCode = pa2.energmetercode
        and pa1.enermetercode = pa2.energmetercode
        and pa1.stationcode = si.id
        and m.statisPeriod = replace(#{budget}, '-', '')
        and m.del_flag = 0
    </select>

    <select id="generateSyncCollect"
            resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeterFail">
        select budget, cityCode, countyCode, stationCode, energyData
        from fail_sync_collectmeter
        where budget = #{budget}
          and del_flag = 0
          and syncFlag = 1
        group by stationCode
    </select>
    <select id="getCollect" resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select stationCode, cityCode, countyCode,cityName,countyName,stationName
        from collectmeter
    </select>
    <select id="getCollectMeterByTime" resultType="com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter">
        select collectTime,
               provinceCode,
               cityCode,
               cityName,
               countyCode,
               countyName,
               stationCode,
               stationName,
               acData,
               acDataSource,
               oepgData,
               oepgDataSource,
               pvpgData,
               pvpgDataSource,
               parentStationCode,
               ccoer,
               cdcf,
               energyData,
               energyDataSource,
               deviceData,
               deviceDataSource,
               productionData,
               productionDataSource,
               managementData,
               managementDataSource,
               businessData,
               businessDataSource,
               otherData,
               otherDataSource
        from collectmeter
        where (del_flag = 0 OR del_flag IS NULL)
    </select>
    <select id="getavgForGeteWay" resultType="java.lang.String">
        SELECT round(avg((ps.station4gquantity + ps.station5gquantity) * 1.6), 2)
        FROM power_station_qua_sta ps
        WHERE ps.resstationcode = #{resstationcode}
          and (ps.currentdate between #{startdate} and #{enddate})
    </select>
    <select id="selectStationGateway" resultType="com.sccl.modules.dataperfect.domain.StationGateway">
        select distinct jt.jtlte_code stationCode
        from power_station_info_r_jtlte jt,
             mss_accountbill ma
        where jt.bill_id = ma.ID
          and ma.year = 2023
          and ma.BIZ_ENTRY_CODE = 11
          and ma.PICKING_MODE in (1,7,9)
          and ma.STATUS=7 and  jt.jtlte_code is not null limit #{limit} offset #{offset}
    </select>
    <select id="selectStationAccountAvgException" resultType="com.sccl.modules.dataperfect.domain.StationGateway">
        SELECT distinct pp.company,
                        pp.country,
                        pp.id ammeterid,
                        (case when pp.category = 1 then pp.ammetername else pp.protocolname end) ammetername,
                        jt.jtlte_tacode                                                            stationCode,
                        pa.startdate                                                             powerStartTime,
                        pa.enddate                                                               powerEndTime,
                        pa.totalusedreadings / (datediff(pa.enddate, pa.startdate) + 1)          billPower
        FROM power_station_info_r_jtlte jt,
             mss_r_billitem_account r,
             power_account pa,
             power_ammeterorprotocol pp,
             mss_accountbill ma
        WHERE r.bill_id = jt.bill_id
          AND r.account_id = pa.pcid
          AND ma.id = r.bill_id
          and pa.ammeterid = pp.id
          and jt.stationid = pp.stationcode
          AND ma.year =           #{year}
          AND ma.BIZ_ENTRY_CODE = #{month}
          and pp.status = 1
          and ma.STATUS = 7
          and jt.jtlte_tacode is not null limit #{limit} offset #{offset}
    </select>
    <select id="selectStationMapTime" resultType="com.sccl.modules.dataperfect.domain.StationGateway">
        SELECT  distinct jt.jtlte_code stationCode,
        pa.startdate powerStartTime,
        pa.enddate powerEndTime,
        pa.totalusedreadings billPower
        FROM power_station_info_r_jtlte jt, mss_r_billitem_account r, power_account pa, power_ammeterorprotocol pp,mss_accountbill ma
        WHERE r.bill_id = jt.bill_id
        AND r.account_id = pa.pcid
        AND ma.id = r.bill_id
        and pa.ammeterid=pp.id
        and jt.stationid=pp.stationcode
        AND jt.jtlte_code IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND ma.year = 2023
        AND ma.BIZ_ENTRY_CODE = 11
        and ma.PICKING_MODE in (1,7,9)
    </select>
    <select id="selectStationDeviceAvg" resultType="com.sccl.modules.dataperfect.domain.StationDeviceAvg">
        select accounting_period time,station_code stationcode,device_power/(datediff(power_end_time,power_start_time)+1) device_avg
        from stationgateway_bak WHERE station_code in
        <foreach collection="list" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectDayAvgs" resultType="com.sccl.modules.dataperfect.domain.StationGateway">

    </select>
    <select id="getavgForGeteWayPro" resultType="com.sccl.modules.dataperfect.domain.StationGateway">
        <trim  suffixOverrides="union all">
            <foreach collection="list" item="item">
                SELECT
                ps.resstationcode stationCode,
                round(avg((ps.station4gquantity + ps.station5gquantity) ), 2) staPower
                FROM power_station_qua_sta ps
                WHERE ps.resstationcode = #{item.stationCode}
                and (ps.currentdate between #{item.powerStartTime} and #{item.powerEndTime})
                union all
            </foreach>
        </trim>
    </select>

    <insert id="insert" parameterType="Syncresult" useGeneratedKeys="false" keyProperty="id">
        <selectKey keyProperty="id" resultType="Long" order="BEFORE">
            select ${@com.sccl.framework.service.IdGenerator@getNextId()} as id from dual
        </selectKey>
        insert into syncresult
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,title,method,result,oper_name,del_flag,msg,num,oper_time,fail_msg
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id}, #{title}, #{method}, #{result}, #{operName},'0', #{msg}, #{num}, #{operTime},#{failMsg}
        </trim>
    </insert>

    <!-- 批量插入 -->
    <insert id="insertList" parameterType="java.util.List" useGeneratedKeys="false">
        insert into syncresult
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            title,
            method,
            result,
            oper_name,
            del_flag,
            msg,
            num,
            oper_time,
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id},
                #{item.title},
                #{item.method},
                #{item.result},
                #{item.operName},
                '0',
                #{item.msg},
                #{item.num},
                #{item.operTime},
            </trim>
        </foreach>
    </insert>
    <insert id="insertMeterInfos">
        INSERT INTO meter_info_db_bases (
        province_code, city_code, city_name, county_code, county_name,
        energy_meter_code, energy_meter_name, status, usagecopy, type,
        station_code, station_name, station_location, station_status,
        station_type, large_industrial_electricity_flag, energy_supply_way,
        power_grid_energy_meter_code, msg_id, sync_result,billid
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.provinceCode}, #{item.cityCode}, #{item.cityName},
            #{item.countyCode}, #{item.countyName}, #{item.energyMeterCode},
            #{item.energyMeterName}, #{item.status}, #{item.usage},
            #{item.type}, #{item.stationCode}, #{item.stationName},
            #{item.stationLocation}, #{item.stationStatus}, #{item.stationType},
            #{item.largeIndustrialElectricityFlag}, #{item.energySupplyWay},
            #{item.powerGridEnergyMeterCode}, #{item.msgId}, #{item.syncResult},#{item.billid}
            )
        </foreach>
    </insert>
    <insert id="insertWriteoffInfos" parameterType="java.util.List">
        INSERT INTO WriteoffInfoDb (
        other_system_main_id, writeoff_instance_code, type, picking_mode,
        county_name, energy_meter_code, energy_meter_name, electricity_start_date, total_quantity_of_electricity,
        contract_price,
        power_consumption, this_electricity_charge, this_quantity_of_electricity, electricity_end_date,
        recovery_electricity_flag,
        this_electricity_price, this_electricity_tax, msg_id, sync_result,billid
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.otherSystemMainId}, #{item.writeoffInstanceCode}, #{item.type}, #{item.pickingMode},
            #{item.countyName}, #{item.energyMeterCode}, #{item.energyMeterName}, #{item.electricityStartDate},
            #{item.totalQuantityOfElectricity}, #{item.contractPrice}, #{item.powerConsumption},
            #{item.thisElectricityCharge},
            #{item.thisQuantityOfElectricity}, #{item.electricityEndDate}, #{item.recoveryElectricityFlag},
            #{item.thisElectricityPrice}, #{item.thisElectricityTax}, #{item.msgId}, #{item.syncResult},#{item.billid}
            )
        </foreach>
    </insert>
    <insert id="insertJzForMeterinfo" parameterType="java.util.List">
        insert
        into meterinfo
        (energyType, typeStationCode, contractPrice, provinceCode, cityCode, cityName, countyCode, countyName,
        energyMeterCode,
        powerGridEnergyMeterCode,energyMeterName,status,usageCopy,type,stationCode,
        stationName,stationLocation,stationStatus,stationType,largeIndustrialElectricityFlag,
        energySupplyWay,siteCode,syncFlag)
        SELECT '45' energyType,
        '1' typeStationCode,
        (case when pap.price is null then 0.65 else pap.price end) contractPrice,
        '26' provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
        (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company) cityName,
        pap.country countyCode,
        (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        ifnull(pap.supplybureauammetercode, '') powerGridEnergyMeterCode,
        ifnull(pap.projectname, '未知') energyMeterName,
        (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END) `status`,
        (select type_code from power_category_type where type_category = 'ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END) type,
        (case when sj.jtlte_code is not null then sj.jtlte_code else '未知' end) stationCode,
        (case when sj.jtlte_name is not null then sj.jtlte_name else '未知' end) stationName,
        ifnull(IFNULL(si.address,
        si.stationname), '未知') stationLocation,
        (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END) stationStatus,
        rpad(pap.electrotype, 4, 0) stationType,
        IFNULL(si.isbigfactories, 0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag, 1) energySupplyWay,
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) siteCode,'3'
        FROM (select *
        from power_ammeterorprotocol
        where del_flag = '0'
        and status = 1
        and electrotype in
        (1411, 1412, 1421, 1422, 1431, 1432)
        ) pap
        inner join power_station_info si on si.id = pap.stationcode
        inner join (select jjt.*
        from power_station_info_r_jtlte jjt
        INNER JOIN (select max(id) mid, stationid
        from power_station_info_r_jtlte
        group by stationid) f on jjt.stationid = f.stationid and jjt.id = f.mid) sj on
        sj.stationid = si.id and sj.jtlte_code in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </insert>
    <insert id="insertJznotForMeterinfo" parameterType="java.util.List">
        insert
        into meterinfo
        (energyType, typeStationCode, contractPrice, provinceCode, cityCode, cityName, countyCode, countyName,
        energyMeterCode,
        powerGridEnergyMeterCode,energyMeterName,status,usageCopy,type,stationCode,
        stationName,stationLocation,stationStatus,stationType,largeIndustrialElectricityFlag,
        energySupplyWay,siteCode,syncFlag)
        SELECT '45' energyType,
        '1' typeStationCode,
        (case when pap.price is null then 0.65 else pap.price end) contractPrice,
        '26' provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
        (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company) cityName,
        pap.country countyCode,
        (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        ifnull(pap.supplybureauammetercode, '') powerGridEnergyMeterCode,
        ifnull(pap.projectname, '未知') energyMeterName,
        (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END) `status`,
        (select type_code from power_category_type where type_category = 'ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END) type,
        (case
        when si.resstationcode is not null then si.resstationcode
        else
        '未知' end) stationCode,
        (case when si.resstationname is not null then si.resstationname else '未知' end) stationName,
        ifnull(IFNULL(si.address,
        si.stationname), '未知') stationLocation,
        (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END) stationStatus,
        rpad(pap.electrotype, 4, 0) stationType,
        IFNULL(si.isbigfactories, 0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag, 1) energySupplyWay,
        (case
        when si.resstationcode is not null then si.resstationcode
        else
        '' end) siteCode,'3'
        FROM (select *
        from power_ammeterorprotocol
        where del_flag = '0'
        and status = 1
        and electrotype not in
        (1411, 1412, 1421, 1422, 1431, 1432)
        and stationaddresscode in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ) pap inner join power_station_info si on si.id = pap.stationcode
    </insert>
    <insert id="insertMeterinfoAll" parameterType="java.util.List">
        insert
        into meterinfo
        (energyType, typeStationCode, contractPrice, provinceCode, cityCode, cityName, countyCode, countyName,
        energyMeterCode,
        powerGridEnergyMeterCode,energyMeterName,status,usageCopy,type,stationCode,
        stationName,stationLocation,stationStatus,stationType,largeIndustrialElectricityFlag,
        energySupplyWay,siteCode)
        SELECT '45' energyType,
        '1' typeStationCode,
        (case when pap.price is null then 0.65 else pap.price end) contractPrice,
        '26' provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
        (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company) cityName,
        pap.country countyCode,
        (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        ifnull(pap.supplybureauammetercode, '') powerGridEnergyMeterCode,
        ifnull(pap.projectname, '未知') energyMeterName,
        (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END) `status`,
        (select type_code from power_category_type where type_category = 'ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END) type,
        (case when sj.jtlte_code is not null then sj.jtlte_code else '未知' end) stationCode,
        (case when sj.jtlte_name is not null then sj.jtlte_name else '未知' end) stationName,
        ifnull(IFNULL(si.address,
        si.stationname), '未知') stationLocation,
        (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END) stationStatus,
        rpad(pap.electrotype, 4, 0) stationType,
        IFNULL(si.isbigfactories, 0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag, 1) energySupplyWay,
        (case when sj.jtlte_code is not null then sj.jtlte_code else '' end) siteCode
        FROM (select *
        from power_ammeterorprotocol
        where del_flag = '0'
        and status = 1
        and electrotype in
        (1411, 1412, 1421, 1422, 1431, 1432)
        ) pap
        inner join power_station_info si on si.id = pap.stationcode
        inner join (select jjt.*
        from power_station_info_r_jtlte jjt
        INNER JOIN (select max(id) mid, stationid
        from power_station_info_r_jtlte
        group by stationid) f on jjt.stationid = f.stationid and jjt.id = f.mid) sj on
        sj.stationid = si.id and sj.jtlte_code in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        union all
        SELECT '45' energyType,
        '1' typeStationCode,
        (case when pap.price is null then 0.65 else pap.price end) contractPrice,
        '26' provinceCode,
        (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
        (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company) cityName,
        pap.country countyCode,
        (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country) countyName,
        (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END) energyMeterCode,
        ifnull(pap.supplybureauammetercode, '') powerGridEnergyMeterCode,
        ifnull(pap.projectname, '未知') energyMeterName,
        (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END) `status`,
        (select type_code from power_category_type where type_category = 'ammeterUse' and type_code = pap.ammeteruse)
        `usage`,
        (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END) type,
        (case
        when si.resstationcode is not null then si.resstationcode
        else
        '未知' end) stationCode,
        (case when si.resstationname is not null then si.resstationname else '未知' end) stationName,
        ifnull(IFNULL(si.address,
        si.stationname), '未知') stationLocation,
        (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END) stationStatus,
        rpad(pap.electrotype, 4, 0) stationType,
        IFNULL(si.isbigfactories, 0) largeIndustrialElectricityFlag,
        IFNULL(pap.directsupplyflag, 1) energySupplyWay,
        (case
        when si.resstationcode is not null then si.resstationcode
        else
        '' end) siteCode
        FROM (select *
        from power_ammeterorprotocol
        where del_flag = '0'
        and status = 1
        and electrotype not in
        (1411, 1412, 1421, 1422, 1431, 1432)
        and stationaddresscode in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ) pap inner join power_station_info si on si.id = pap.stationcode
    </insert>
    <insert id="insertMeterinfos">
        insert
        into meterinfo
        (energyType, typeStationCode, contractPrice, provinceCode, cityCode, cityName, countyCode, countyName,
         energyMeterCode,
         powerGridEnergyMeterCode, energyMeterName, status, usageCopy, type, stationCode,
         stationName, stationLocation, stationStatus, stationType, largeIndustrialElectricityFlag,
         energySupplyWay, siteCode)
        SELECT '45'                                                                               energyType,
               '1'                                                                                typeStationCode,
               (case when pap.price is null then 0.65 else pap.price end)                         contractPrice,
               '26'                                                                               provinceCode,
               (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
               (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company)           cityName,
               pap.country                                                                        countyCode,
               (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country)                countyName,
               (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END)        energyMeterCode,
               ifnull(pap.supplybureauammetercode, '')                                            powerGridEnergyMeterCode,
               ifnull(pap.projectname, '未知')                                                      energyMeterName,
               (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END)                         `status`,
               (select type_code
                from power_category_type
                where type_category = 'ammeterUse'
                  and type_code = pap.ammeteruse)
                                                                                                  `usage`,
               (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END)                                type,
               (case when sj.jtlte_code is not null then sj.jtlte_code else '未知' end)             stationCode,
               (case when sj.jtlte_name is not null then sj.jtlte_name else '未知' end)             stationName,
               ifnull(IFNULL(si.address,
                             si.stationname), '未知')                                               stationLocation,
               (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END)                                        stationStatus,
               rpad(pap.electrotype, 4, 0)                                                        stationType,
               IFNULL(si.isbigfactories, 0)                                                       largeIndustrialElectricityFlag,
               IFNULL(pap.directsupplyflag, 1)                                                    energySupplyWay,
               (case when sj.jtlte_code is not null then sj.jtlte_code else '' end)               siteCode
        FROM (select *
              from power_ammeterorprotocol
              where del_flag = '0'
                and status = 1
                and electrotype in
                    (1411, 1412, 1421, 1422, 1431, 1432)
             ) pap
                 inner join power_station_info si on si.id = pap.stationcode
                 inner join (select jjt.*
                             from power_station_info_r_jtlte jjt
                                      INNER JOIN (select max(id) mid, stationid
                                                  from power_station_info_r_jtlte
                                                  group by stationid) f
                                                 on jjt.stationid = f.stationid and jjt.id = f.mid) sj on
            sj.stationid = si.id and sj.jtlte_code = #{stationcode}
        union all
        SELECT '45'                                                                               energyType,
               '1'                                                                                typeStationCode,
               (case when pap.price is null then 0.65 else pap.price end)                         contractPrice,
               '26'                                                                               provinceCode,
               (select max(v_org_code) from power_city_organization where pap.company = org_code) cityCode,
               (SELECT max(org_name) FROM rmp.sys_organizations WHERE id = pap.company)           cityName,
               pap.country                                                                        countyCode,
               (SELECT org_name FROM rmp.sys_organizations WHERE id = pap.country)                countyName,
               (CASE WHEN pap.category = 1 THEN pap.ammetername ELSE pap.protocolname END)        energyMeterCode,
               ifnull(pap.supplybureauammetercode, '')                                            powerGridEnergyMeterCode,
               ifnull(pap.projectname, '未知')                                                      energyMeterName,
               (CASE pap.`status` WHEN 1 THEN 1 WHEN 0 THEN 0 ELSE 1 END)                         `status`,
               (select type_code
                from power_category_type
                where type_category = 'ammeterUse'
                  and type_code = pap.ammeteruse)
                                                                                                  `usage`,
               (CASE pap.isentityammeter WHEN 0 THEN 2 ELSE 1 END)                                type,
               (case
                    when si.resstationcode is not null then si.resstationcode
                    else
                        '未知' end)                                                                 stationCode,
               (case when si.resstationname is not null then si.resstationname else '未知' end)     stationName,
               ifnull(IFNULL(si.address,
                             si.stationname), '未知')                                               stationLocation,
               (CASE si.`status` WHEN 2 THEN 1 ELSE 0 END)                                        stationStatus,
               rpad(pap.electrotype, 4, 0)                                                        stationType,
               IFNULL(si.isbigfactories, 0)                                                       largeIndustrialElectricityFlag,
               IFNULL(pap.directsupplyflag, 1)                                                    energySupplyWay,
               (case
                    when si.resstationcode is not null then si.resstationcode
                    else
                        '' end)                                                                   siteCode
        FROM (select *
              from power_ammeterorprotocol
              where del_flag = '0'
                and status = 1
                and electrotype not in
                    (1411, 1412, 1421, 1422, 1431, 1432)
                and stationaddresscode = #{stationcode}
             ) pap
                 inner join power_station_info si on si.id = pap.stationcode
    </insert>
    <insert id="insertFailCollect">
        INSERT INTO fail_sync_collectmeter (
        collectTime,
        cityCode,
        cityName,
        countyCode,
        countyName,
        stationCode,
        stationName,
        acData,
        acDataSource,
        oepgData,
        oepgDataSource,
        pvpgData,
        pvpgDataSource,
        parentStationCode,
        ccoer,
        cdcf,
        energyData,
        energyDataSource,
        deviceData,
        deviceDataSource,
        productionData,
        productionDataSource,
        managementData,
        managementDataSource,
        businessData,
        businessDataSource,
        otherData,
        otherDataSource,
        syncFlag,
        failMag,
        sync_time,
        budget
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.collectTime},
            #{item.cityCode},
            #{item.cityName},
            #{item.countyCode},
            #{item.countyName},
            #{item.stationCode},
            #{item.stationName},
            #{item.acData},
            #{item.acDataSource},
            #{item.oepgData},
            #{item.oepgDataSource},
            #{item.pvpgData},
            #{item.pvpgDataSource},
            #{item.parentStationCode},
            #{item.ccoer},
            #{item.cdcf},
            #{item.energyData},
            #{item.energyDataSource},
            #{item.deviceData},
            #{item.deviceDataSource},
            #{item.productionData},
            #{item.productionDataSource},
            #{item.managementData},
            #{item.managementDataSource},
            #{item.businessData},
            #{item.businessDataSource},
            #{item.otherData},
            #{item.otherDataSource},
            #{item.syncFlag},
            #{item.failMag},
            #{item.sync_time},
            #{item.budget}
            )
        </foreach>
    </insert>
    <insert id="insertMeterinfoBitch" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO meterinfo (
        provinceCode, cityCode, cityName, countyCode, countyName, energyMeterCode, energyMeterName, status,
        usageCopy, type, stationCode, stationName, stationLocation, stationStatus, stationType,
        largeIndustrialElectricityFlag, energySupplyWay, siteCode, powerGridEnergyMeterCode, del_flag,
        energyType, typeStationCode, contractPrice, syncFlag
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.provinceCode}, #{item.cityCode}, #{item.cityName}, #{item.countyCode}, #{item.countyName},
            #{item.energyMeterCode}, #{item.energyMeterName}, #{item.status}, #{item.usage}, #{item.type},
            #{item.stationCode}, #{item.stationName}, #{item.stationLocation}, #{item.stationStatus},
            #{item.stationType}, #{item.largeIndustrialElectricityFlag}, #{item.energySupplyWay},
            #{item.siteCode}, #{item.powerGridEnergyMeterCode}, '0', #{item.energyType},
            #{item.typeStationCode}, #{item.contractPrice}, '0'
            )
        </foreach>
    </insert>
    <insert id="insertStaTemp">
        insert into StaTemp
        select *
        from power_station_qua_sta where resstationcode in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </insert>
    <insert id="insertListForDelayCache" parameterType="java.util.List">
        INSERT INTO delay_cache (delay_key, expire_time, time, status)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.delay_key}, #{item.expire_time}, #{item.time}, #{item.status})
        </foreach>
    </insert>
    <insert id="insertStaResult">
        INSERT INTO sta_result (resstationcode, startdate, enddate, avgForBill, avgForGateway, diff)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.resstationcode}, #{item.startdate}, #{item.enddate}, #{item.avgForBill}, #{item.avgForGateway},
            concat(#{item.diff},'%'))
        </foreach>
    </insert>
    <insert id="insertStagateway2">
        insert into stationgateway2 (stationCode,devicePower,powerStartTime,powerEndTime,billPower)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.stationCode},
                #{item.devicePower},
                #{item.powerStartTime},
                #{item.powerEndTime},
                #{item.billPower},
            </trim>
        </foreach>
    </insert>
    <insert id="insertAccountAvgException">
        insert into AccountAvgException (company, country,ammetername, stationCode, powerStartTime, powerEndTime, avgbillPower, avgstaPower,time,ammeterid)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.company},
                #{item.country},
                #{item.ammetername},
                #{item.stationCode},
                #{item.powerStartTime},
                #{item.powerEndTime},
                #{item.billPower},
                #{item.staPower},
                #{item.time},
                #{item.ammeterid},
            </trim>
        </foreach>
    </insert>
    <update id="updateByPrimaryKey" parameterType="Syncresult">
        update syncresult
        <trim prefix="SET" suffixOverrides=",">
            title = #{title},
            method = #{method},
            result = #{result},
            oper_name = #{operName},
            del_flag = #{delFlag},
            msg = #{msg},
            num = #{num},
            oper_time = #{operTime},
        </trim>
        where id = #{id}
    </update>

    <update id="updateForModel" parameterType="Syncresult">
        update syncresult
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null  and title != ''  ">title = #{title},</if>
            <if test="method != null  ">method = #{method},</if>
            <if test="result != null  and result != ''  ">result = #{result},</if>
            <if test="operName != null  and operName != ''  ">oper_name = #{operName},</if>
            <if test="delFlag != null  and delFlag != ''  ">del_flag = #{delFlag},</if>
            <if test="msg != null  and msg != ''  ">msg = #{msg},</if>
            <if test="num != null  ">num = #{num},</if>
            <if test="operTime != null  ">oper_time = #{operTime},</if>
        </trim>
        where id = #{id}
    </update>
    <!-- 逻辑删除 -->
    <update id="deleteByPrimaryKey" parameterType="Map">
        UPDATE syncresult SET DEL_FLAG='1' where id = #{id}
        <if test="shardKey != null and shardKey != ''">and shardKey = #{shardKey}</if>
    </update>

    <update id="deleteByIds" parameterType="String">
        UPDATE syncresult SET DEL_FLAG='1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 物理删除 -->
    <delete id="deleteByPrimaryKeyDB" parameterType="Map">
        delete from syncresult where id = #{id}
        <if test="shardKey != null and shardKey != ''">and shardKey = #{shardKey}</if>
    </delete>

    <delete id="deleteByIdsDB" parameterType="String">
        delete from syncresult where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteAll">
        update fail_sync_collectmeter
        set del_flag = 1
        where budget = #{budget};
    </delete>
    <delete id="deleteAccountAvgException">
        update accountavgexception
        set del_flag =1
        where time =#{time}
    </delete>
    <update id="deleteMeterinoBitch">
        UPDATE meterinfo
        SET del_flag = 1
        WHERE (provinceCode, cityCode, countyCode, energyMeterCode) IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            (#{item.provinceCode}, #{item.cityCode}, #{item.countyCode}, #{item.energyMeterCode})
        </foreach>
        and del_flag=0
    </update>
    <update id="delMeterinfoForStatoncode">
        <if test="list != null and list.size() > 0">
            update meterinfo
            set del_flag =1
            where stationCode in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="list == null or list.size() == 0">
            update meterinfo set del_flag =0 where 1=0
        </if>
    </update>
    <update id="updateRetryFailCollecter">
        UPDATE fail_sync_collectmeter
        <set>
            retry = retry + 1,
            syncFlag = CASE id
            <foreach collection="list" item="collectMeterFail" separator=" ">
                WHEN #{collectMeterFail.id} THEN #{collectMeterFail.syncFlag}
            </foreach>
            END,
            failMag = CASE id
            <foreach collection="list" item="collectMeterFail" separator=" ">
                WHEN #{collectMeterFail.id} THEN #{collectMeterFail.failMag}
            </foreach>
            END
        </set>
        WHERE id IN
        <foreach collection="list" item="collectMeterFail" open="(" close=")" separator=",">
            #{collectMeterFail.id}
        </foreach>
    </update>
    <update id="updateDelayCache">
        update delay_cache
        set status =#{status}
        where delay_key = #{key}
          and expire_time = #{expireTime}
    </update>

    <update id="updateRoomEnergyUse">
        UPDATE machineroomenergy
        <set>
            retry = retry + 1,
            syncFlag = CASE id
            <foreach collection="list" item="collectMeterFail" separator=" ">
                WHEN #{collectMeterFail.id} THEN #{collectMeterFail.syncFlag}
            </foreach>
            END,
            failMag = CASE id
            <foreach collection="list" item="collectMeterFail" separator=" ">
                WHEN #{collectMeterFail.id} THEN #{collectMeterFail.failMag}
            </foreach>
            END
        </set>
        WHERE id IN
        <foreach collection="list" item="collectMeterFail" open="(" close=")" separator=",">
            #{collectMeterFail.id}
        </foreach>
    </update>
    <update id="updateStationEnergyUse">
        UPDATE stationenergyuse
        <set>
            retry = retry + 1,
            syncFlag = CASE id
            <foreach collection="list" item="collectMeterFail" separator=" ">
                WHEN #{collectMeterFail.id} THEN #{collectMeterFail.syncFlag}
            </foreach>
            END,
            failMag = CASE id
            <foreach collection="list" item="collectMeterFail" separator=" ">
                WHEN #{collectMeterFail.id} THEN #{collectMeterFail.failMag}
            </foreach>
            END
        </set>
        WHERE id IN
        <foreach collection="list" item="collectMeterFail" open="(" close=")" separator=",">
            #{collectMeterFail.id}
        </foreach>
    </update>

    <select id="selectAllProGroupCheck" resultType="com.sccl.modules.dataperfect.domain.AccountTime">
        SELECT
            *
        FROM jt_gw_check
        where type = '5'
          and statisPeriod = REPLACE ( #{budget}, '-', '' )
    </select>
    <delete id="deleteNowBudget">
        delete from jt_gw_check
        where statisPeriod = REPLACE ( #{budget}, '-', '' )
    </delete>
    <select id="collectAllProGroupChecknewAll" resultType="com.sccl.modules.dataperfect.domain.AccountTimeVo">
        SELECT
            DISTINCT
            mj.stationcode stationaddresscode,
            mj.energyMeterCode,
            wb.this_quantity_of_electricity energyData,
            wb.electricity_start_date startdate,
            wb.electricity_end_date enddate,
            REPLACE (#{budget}, '-', '' ) statisPeriod,
            wb.writeoff_instance_code,
            '5' type,
            mj.stationType
        FROM
            (
                SELECT
                    gg.*
                FROM
                    ( SELECT max( msg_id ) mmsgid, billid FROM writeoffinfodb wdd GROUP BY billid ) dd
                        INNER JOIN writeoffinfodb gg ON gg.msg_id = dd.mmsgid
                        AND dd.billid = gg.billid
            ) wb
                LEFT JOIN meterinfo_all_jt mj ON wb.energy_meter_code = mj.energyMeterCode
        WHERE
            wb.sync_result = '成功'
          AND wb.picking_mode IN ( 1, 7, 9 )
          AND wb.county_name = REPLACE (#{budget}, '-', '' )
          AND wb.create_time &lt; DATE_ADD(
                concat(#{budget}, '-01'), INTERVAL 1 MONTH)
          AND mj.stationType IN ( '1110',
                               '1120',
                               '1130',
                               '1210',
                               '1220',
                               '1310',
                               '1320',
                               '1330',
                               '1411',
                               '1412',
                               '1421',
                               '1422',
                               '1431',
                               '1432' )
          AND billid IN (
            SELECT
                id
            FROM
                mss_accountbill
            WHERE
                  YEAR = LEFT ( #{budget},4)
          AND BIZ_ENTRY_CODE = SUBSTRING(REPLACE (#{budget}, '-', '' ), -2)
            )
    </select>
    <insert id="insetAllProGroupCheck">
        insert into jt_gw_check (stationaddresscode,energyMeterCode,energyData,statisPeriod,startdate,enddate,`type`,writeoff_instance_code, stationType)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.stationaddresscode},
                #{item.energyMeterCode},
                #{item.energyData},
                #{item.statisPeriod},
                #{item.startdate},
                #{item.enddate},
                #{item.type},
                #{item.writeoffInstanceCode},
                #{item.stationType}
            </trim>
        </foreach>
    </insert>
    <select id="selectAllProGroupCheckQxm" resultType="java.util.HashMap">
        SELECT
            t.*,
            psi.resstationname stationName,
            jmb.city cityCode,
            jmb.cityName,
            jmb.country countryCode,
            jmb.countryName
        FROM
            (
                SELECT
                    stationaddresscode stationCode,
                    stationType,
                    STR_TO_DATE( startdate, '%Y%m%d' ) startdate,
                    STR_TO_DATE( enddate, '%Y%m%d' ) enddate,
                    ROUND( SUM( energyData ), 2 ) AS total_data,
                    1 + DATEDIFF(
                            STR_TO_DATE( enddate, '%Y%m%d' ),
                            STR_TO_DATE( startdate, '%Y%m%d' )) AS diff_days,
                    ROUND( SUM( energyData ) / ( 1 + DATEDIFF( STR_TO_DATE( enddate, '%Y%m%d' ), STR_TO_DATE( startdate, '%Y%m%d' ))), 2 ) AS avg_data
                FROM
                    jt_gw_check
                WHERE
                    type = '5'
                  AND statisPeriod = REPLACE ( #{budget}, '-', '' )
                GROUP BY
                    stationaddresscode,
                    startdate,
                    enddate
            ) t
                LEFT JOIN power_station_info psi ON psi.resstationcode = t.stationCode AND psi.`status`=2
                LEFT JOIN (
                        SELECT
                            *
                        FROM
                            jt_mss_busi
                        WHERE
                            id IN ( SELECT id FROM ( SELECT stationcode, max( id ) AS id FROM jt_mss_busi GROUP BY stationcode ) t )
                    ) jmb ON t.stationCode = jmb.stationcode
        WHERE
            jmb.id IS NOT NULL
          AND psi.id IS NOT NULL
    </select>
    <select id="manualAcquisition" resultType="com.sccl.modules.dataperfect.domain.AccountTime">
        select * from jt_mss_busi_test;
    </select>
    <select id="selectYcyzNullStationCodes" resultType="java.lang.String">
        select station_code
        from ycyz_null
        where station_code is not null
          and station_code != ''
    </select>
</mapper>
