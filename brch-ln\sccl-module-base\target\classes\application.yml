# 项目相关配置
sccl:
  #名称
  name: sccl-basic-frame
  #版本
  version: 1.0
  #版权年份
  copyrightYear: 2018
  #头像上传路径
  #profile: /Users/<USER>/temp/profile/
  # 获取ip地址开关
  addressEnabled: false
  #jwt 过期时间 单位 分钟
  jwtExpiredTime: 720
  #jwt 加密密钥
  jwtkey: sccl-web-jwt-encrypt-key
#开发环境配置

  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    #max-threads: 200
    # Tomcat启动初始化的线程数，默认值25
    #min-spare-threads: 30
#用户配置
user:
  password:
    #密码错误{maxRetryCount}次锁定10分钟
    maxRetryCount: 5
#Spring配置
spring:
  thymeleaf:
    mode: HTML
    encoding: utf-8
    # 禁用缓存
    cache: false
  messages:
    #国际化资源文件路径
    basename: i18n/messages
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  profiles:
    active: sc
  #文件上传
  servlet:
    multipart:
      max-file-size:  30MB
      max-request-size:  30MB
  devtools:
    restart:
      #热部署开关
      enabled: true
      #mongodb
      #data :
      #  mongodb :
      #    nodes: *************:27017
      #    user: sccl
      #    password: sccl
      #    connectionsPerHost: 27017
      #    dbsName: sccldb
      #连接超时，推荐>3000毫秒
      #    connectTimeout : 3000
      #redis配置
      #redis:
      #单机环境
      #host: 127.0.0.1
      #port: 6379
      #  password: 123456
      #客户端超时时间单位是毫秒 默认是2000
      #  timeout: 2000
      #集群环境打开下面注释，单机不需要打开
      #  cluster:
      #集群信息
      #    nodes:
      #      - *************:7001
      #      - *************:7002
      #      - *************:7003
      #      - *************:7004
      #      - *************:7005
      #      - *************:7006
      #默认值是5 一般当此值设置过大时，容易报：Too many Cluster redirections
    #    maxRedirects: 3
    #连接池的最大数据库连接数。设为0表示无限制,如果是jedis 2.4以后用redis.maxTotal
    #  max-active: 600
    #  max-wait: 1
    #最大空闲数
    #  max-idle: 8
    #  min-idle: 0
    #rabbitMQ
    #rabbitmq:
    #  host: *************
    #    host: 127.0.0.1
    #  port: 5672
    #  username: admin
    #  password: admin123
    #MQ的虚拟主机名称
  #    virtual-host: /
  #  virtual-host: lcyq
# MyBatis
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.sccl.modules
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mybatis/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper 单数据源
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql
# PageHelper 多数据源
#pagehelper:
#  reasonable: false
#  supportMethodsArguments: true
#  params: count=countSql
#  # 默认false,当为true时，自动检验适合的数据库
#  auto-dialect: true
#  # 这个一定要加上，不然mysql和oracle分页两个只能用一个，另一个会报错，加上后，两中数据库分页都可以用了
#  auto-runtime-dialect: true

# Shiro
shiro:
  user:
    # 登录地址
    loginUrl: /login/login
    # 权限认证失败地址
    unauthorizedUrl: /unauth
    # 首页地址
    indexUrl: /index
    #后台授权
    authz: false,
    # 验证码开关
    captchaEnabled: false
    # 验证码类型 math 数组计算 char 字符
    captchaType: math
  cookie:
    # 设置Cookie的域名 默认空，即当前访问的域名
    domain:
    # 设置cookie的有效访问路径
    path: /
    # 设置HttpOnly属性
    httpOnly: true
    # 设置Cookie的过期时间，天为单位
    maxAge: 30
  session:
    # Session超时时间（默认30分钟）
    expireTime: 720
    # 同步session到数据库的周期（默认1分钟）
    dbSyncPeriod: 1
    # 相隔多久检查一次session的有效性，默认就是10分钟
    validationInterval: 10
# 防止XSS攻击
xss:
  # 过滤开关
  enabled: false
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*,

    #附件
    #attachments:
    #  task:
    #核心线程数
    #    CorePoolSize: 200
    #最大线程数
    #    MaxPoolSize: 500
    #队列最大长度 >=mainExecutor.maxSize
    #    QueueCapacity: 250
  #线程池维护线程所允许的空闲时间
#    keepAlive: 60

#雪花ID生成器版本
snowflakeID: 1.6
minio:
  url: http://oss1.paas.sc.ctc.com/ #新对象存储服务的URL #http://oss1.paas.sc.ctc.com/ http://10.251.66.21
  accessKey: QGSSN4883J54HRGW5BKV #Access key账户
  secretKey: cPPvBaSMKLPqOhAAKGtc2EPAEAv3aBFdhz9m9YXb  #Secret key密码





