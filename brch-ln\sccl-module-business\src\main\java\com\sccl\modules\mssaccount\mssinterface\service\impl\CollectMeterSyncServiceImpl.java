package com.sccl.modules.mssaccount.mssinterface.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sccl.modules.business.ammeterorprotocol.mapper.AmmeterorprotocolMapper;
import com.sccl.modules.mssaccount.mssinterface.domain.CollectMeter;
import com.sccl.modules.mssaccount.mssinterface.domain.CollectMeterQueryResult;
import com.sccl.modules.mssaccount.mssinterface.service.CollectMeterSyncService;
import com.sccl.modules.mssaccount.mssinterface.service.MssJsonClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 报账明细电量推送服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
@Slf4j
public class CollectMeterSyncServiceImpl implements CollectMeterSyncService {

    @Autowired
    private AmmeterorprotocolMapper ammeterorprotocolMapper;

    @Autowired
    private MssJsonClient mssJsonClient;

    @Override
    public String syncAllCollectMeterByPeriod(String periodNumber) {
        try {
            log.info("开始推送全部报账明细电量，期号：{}", periodNumber);
            
            // 1. 查询collectmeter_v2_期号表的全部数据
            List<CollectMeterQueryResult> queryResults = ammeterorprotocolMapper.getAllCollectMeterByPeriod(periodNumber);
            
            if (CollUtil.isEmpty(queryResults)) {
                log.info("期号 {} 对应的表 collectmeter_v2_{} 中未查询到数据", periodNumber, periodNumber);
                return "未查询到数据";
            }
            
            log.info("查询到{}条记录", queryResults.size());
            
            // 2. 转换为CollectMeter对象
            List<CollectMeter> collectMeterList = new ArrayList<>();
            for (CollectMeterQueryResult queryResult : queryResults) {
                CollectMeter collectMeter = convertToCollectMeterFromTable(queryResult, periodNumber);
                collectMeterList.add(collectMeter);
            }
            
            // 3. 处理数据，满足协议格式
            CollectMeter.processDataFields(collectMeterList);
            
            // 4. 批量推送数据
            if (!collectMeterList.isEmpty()) {
                batchPushCollectMeterData(collectMeterList);
            }
            
            String resultMsg = String.format("推送全部报账明细电量完成，期号：%s，共推送%d条数据", 
                    periodNumber, collectMeterList.size());
            log.info(resultMsg);
            return resultMsg;
            
        } catch (Exception e) {
            log.error("推送全部报账明细电量过程中发生异常，期号：{}", periodNumber, e);
            return "推送失败: " + e.getMessage();
        }
    }

    @Override
    public String getTableStats(String periodNumber) {
        try {
            log.info("查询期号表统计信息，期号：{}", periodNumber);
            
            // 查询数据总数
            List<CollectMeterQueryResult> queryResults = ammeterorprotocolMapper.getAllCollectMeterByPeriod(periodNumber);
            
            if (CollUtil.isEmpty(queryResults)) {
                return String.format("表 collectmeter_v2_%s 中无数据", periodNumber);
            }
            
            int totalCount = queryResults.size();
            
            // 统计不同状态的数据
            long syncedCount = queryResults.stream()
                    .filter(result -> result.getSyncFlag() != null && result.getSyncFlag() == 1)
                    .count();
            
            long unsyncedCount = totalCount - syncedCount;
            
            String resultMsg = String.format("表 collectmeter_v2_%s 统计信息：总数据量=%d条，已同步=%d条，未同步=%d条", 
                    periodNumber, totalCount, syncedCount, unsyncedCount);
            
            log.info(resultMsg);
            return resultMsg;
            
        } catch (Exception e) {
            log.error("查询期号表统计信息过程中发生异常，期号：{}", periodNumber, e);
            return "查询失败: " + e.getMessage();
        }
    }

    @Override
    public String syncCollectMeterByPage(String periodNumber, Integer pageSize, Integer maxPages) {
        try {
            log.info("开始分页推送报账明细电量，期号：{}，每页大小：{}，最大页数：{}", periodNumber, pageSize, maxPages);
            
            // 1. 先获取总数据量
            List<CollectMeterQueryResult> allResults = ammeterorprotocolMapper.getAllCollectMeterByPeriod(periodNumber);
            
            if (CollUtil.isEmpty(allResults)) {
                log.info("期号 {} 对应的表 collectmeter_v2_{} 中未查询到数据", periodNumber, periodNumber);
                return "未查询到数据";
            }
            
            int totalCount = allResults.size();
            int totalPages = (totalCount + pageSize - 1) / pageSize;
            
            // 如果设置了最大页数限制
            if (maxPages != null && maxPages < totalPages) {
                totalPages = maxPages;
            }
            
            log.info("总数据量：{}条，每页：{}条，总页数：{}", totalCount, pageSize, totalPages);
            
            int totalPushed = 0;
            
            // 2. 分页处理数据
            for (int page = 0; page < totalPages; page++) {
                int startIndex = page * pageSize;
                int endIndex = Math.min(startIndex + pageSize, allResults.size());
                
                List<CollectMeterQueryResult> pageResults = allResults.subList(startIndex, endIndex);
                
                // 转换为CollectMeter对象
                List<CollectMeter> collectMeterList = new ArrayList<>();
                for (CollectMeterQueryResult queryResult : pageResults) {
                    CollectMeter collectMeter = convertToCollectMeterFromTable(queryResult, periodNumber);
                    collectMeterList.add(collectMeter);
                }
                
                // 处理数据格式
                CollectMeter.processDataFields(collectMeterList);
                
                // 推送当前页数据
                if (!collectMeterList.isEmpty()) {
                    batchPushCollectMeterData(collectMeterList);
                    totalPushed += collectMeterList.size();
                }
                
                log.info("第{}页推送完成，本页{}条数据，累计推送{}条", page + 1, collectMeterList.size(), totalPushed);
                
                // 页间休眠，避免推送过快
                if (page < totalPages - 1) {
                    try {
                        Thread.sleep(1000); // 休眠1秒
                    } catch (InterruptedException e) {
                        log.warn("页间休眠被中断", e);
                    }
                }
            }
            
            String resultMsg = String.format("分页推送报账明细电量完成，期号：%s，总页数：%d，共推送%d条数据", 
                    periodNumber, totalPages, totalPushed);
            log.info(resultMsg);
            return resultMsg;
            
        } catch (Exception e) {
            log.error("分页推送报账明细电量过程中发生异常，期号：{}", periodNumber, e);
            return "推送失败: " + e.getMessage();
        }
    }

    /**
     * 将从collectmeter_v2_期号表查询的结果转换为CollectMeter对象
     */
    private CollectMeter convertToCollectMeterFromTable(CollectMeterQueryResult queryResult, String periodNumber) {
        CollectMeter collectMeter = new CollectMeter();
        
        // 设置基本信息
        collectMeter.setCollectTime(periodNumber);
        collectMeter.setCityCode(queryResult.getCityCode());
        collectMeter.setCityName(queryResult.getCityName());
        collectMeter.setCountyCode(queryResult.getCountyCode());
        collectMeter.setCountyName(queryResult.getCountyName());
        collectMeter.setStationCode(queryResult.getStationCode());
        collectMeter.setStationName(queryResult.getStationName());
        collectMeter.setParentStationCode(queryResult.getParentStationCode() != null ? queryResult.getParentStationCode() : "");
        collectMeter.setCcoer(queryResult.getCcoer() != null ? queryResult.getCcoer() : "");
        collectMeter.setCdcf(queryResult.getCdcf() != null ? queryResult.getCdcf() : "");
        
        // 设置电量数据
        collectMeter.setEnergyData(String.valueOf(queryResult.getEnergyData() != null ? queryResult.getEnergyData() : 0.0));
        collectMeter.setEnergyDataSource(String.valueOf(queryResult.getEnergyDataSource() != null ? queryResult.getEnergyDataSource() : 223));
        collectMeter.setAcData(String.valueOf(queryResult.getAcData() != null ? queryResult.getAcData() : 0.0));
        collectMeter.setAcDataSource(String.valueOf(queryResult.getAcDataSource() != null ? queryResult.getAcDataSource() : 223));
        collectMeter.setOepgData(String.valueOf(queryResult.getOepgData() != null ? queryResult.getOepgData() : 0.0));
        collectMeter.setOepgDataSource(String.valueOf(queryResult.getOepgDataSource() != null ? queryResult.getOepgDataSource() : 2));
        collectMeter.setPvpgData(String.valueOf(queryResult.getPvpgData() != null ? queryResult.getPvpgData() : 0.0));
        collectMeter.setPvpgDataSource(String.valueOf(queryResult.getPvpgDataSource() != null ? queryResult.getPvpgDataSource() : 2));
        collectMeter.setDeviceData(String.valueOf(queryResult.getDeviceData() != null ? queryResult.getDeviceData() : 0.0));
        collectMeter.setDeviceDataSource(String.valueOf(queryResult.getDeviceDataSource() != null ? queryResult.getDeviceDataSource() : 2));
        collectMeter.setProductionData(String.valueOf(queryResult.getProductionData() != null ? queryResult.getProductionData() : 0.0));
        collectMeter.setProductionDataSource(String.valueOf(queryResult.getProductionDataSource() != null ? queryResult.getProductionDataSource() : 2));
        collectMeter.setManagementData(String.valueOf(queryResult.getManagementData() != null ? queryResult.getManagementData() : 0.0));
        collectMeter.setManagementDataSource(String.valueOf(queryResult.getManagementDataSource() != null ? queryResult.getManagementDataSource() : 2));
        collectMeter.setBusinessData(String.valueOf(queryResult.getBusinessData() != null ? queryResult.getBusinessData() : 0.0));
        collectMeter.setBusinessDataSource(String.valueOf(queryResult.getBusinessDataSource() != null ? queryResult.getBusinessDataSource() : 2));
        collectMeter.setOtherData(String.valueOf(queryResult.getOtherData() != null ? queryResult.getOtherData() : 0.0));
        collectMeter.setOtherDataSource(String.valueOf(queryResult.getOtherDataSource() != null ? queryResult.getOtherDataSource() : 2));
        
        return collectMeter;
    }

    /**
     * 批量推送CollectMeter数据
     */
    private void batchPushCollectMeterData(List<CollectMeter> collectMeterList) {
        int size = collectMeterList.size();
        int batchSize = 50;
        int totalBatches = (size + batchSize - 1) / batchSize; // 向上取整

        for (int i = 0; i < totalBatches; i++) {
            int startIndex = i * batchSize;
            int endIndex = Math.min(startIndex + batchSize, size);
            List<CollectMeter> batch = collectMeterList.subList(startIndex, endIndex);

            try {
                String response = mssJsonClient.syncCollectMeterInfors(batch);
                log.info("第{}批数据:{}条推送完成，响应:{}", i + 1, batch.size(), response);
            } catch (Exception e) {
                log.error("第{}批数据推送失败: {}", i + 1, e.getMessage());
            }
        }
    }
}
