package com.sccl.modules.mssaccount.mssinterface.service;

/**
 * 报账明细电量推送服务接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface CollectMeterSyncService {

    /**
     * 推送全部报账明细电量
     * 根据期号查询对应的collectmeter_v2_期号表，推送全部数据
     *
     * @param periodNumber 期号，例如：202507
     * @return 推送结果
     */
    String syncAllCollectMeterByPeriod(String periodNumber);

    /**
     * 查询指定期号表的数据统计信息
     *
     * @param periodNumber 期号，例如：202507
     * @return 统计信息
     */
    String getTableStats(String periodNumber);

    /**
     * 分页推送报账明细电量
     * 支持大数据量分批推送
     *
     * @param periodNumber 期号，例如：202507
     * @param pageSize 每页大小
     * @param maxPages 最大页数，null表示不限制
     * @return 推送结果
     */
    String syncCollectMeterByPage(String periodNumber, Integer pageSize, Integer maxPages);
}
